import express from 'express';
import { body, validationResult, query } from 'express-validator';
import Attendance from '../models/Attendance.js';
import Student from '../models/Student.js';
import Course from '../models/Course.js';
import Faculty from '../models/Faculty.js';
import { auth, authorize } from '../middleware/auth.js';

const router = express.Router();

// @desc    Mark attendance for students
// @route   POST /api/attendance/mark
// @access  Private (Faculty only)
router.post('/mark', [
  auth,
  authorize('faculty', 'admin'),
  body('students').isArray().withMessage('Students must be an array'),
  body('students.*.studentId').isMongoId().withMessage('Valid student ID required'),
  body('students.*.status').isIn(['Present', 'Absent', 'Late', 'Excused']).withMessage('Invalid status'),
  body('courseId').isMongoId().withMessage('Valid course ID required'),
  body('date').isISO8601().withMessage('Valid date required'),
  body('sessionType').isIn(['Lecture', 'Lab', 'Tutorial', 'Seminar', 'Exam']).withMessage('Invalid session type'),
  body('location').notEmpty().withMessage('Location is required'),
  body('duration').optional().isInt({ min: 15, max: 300 }).withMessage('Duration must be between 15-300 minutes')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { students, courseId, date, sessionType, location, duration = 60, notes } = req.body;
    const facultyId = req.user.id;

    // Verify course exists and faculty has access
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    const attendanceRecords = [];
    const errors = [];

    for (const studentData of students) {
      try {
        // Check if attendance already exists for this date and session
        const existingAttendance = await Attendance.findOne({
          student: studentData.studentId,
          course: courseId,
          date: new Date(date),
          sessionType: sessionType
        });

        if (existingAttendance) {
          // Update existing record
          existingAttendance.status = studentData.status;
          existingAttendance.markedBy = facultyId;
          existingAttendance.notes = studentData.notes || notes || '';
          existingAttendance.timeIn = new Date();
          await existingAttendance.save();
          attendanceRecords.push(existingAttendance);
        } else {
          // Create new record
          const attendanceRecord = new Attendance({
            student: studentData.studentId,
            course: courseId,
            faculty: course.faculty[0] || facultyId,
            date: new Date(date),
            status: studentData.status,
            sessionType: sessionType,
            location: location,
            duration: duration,
            notes: studentData.notes || notes || '',
            markedBy: facultyId,
            semester: course.semester
          });

          await attendanceRecord.save();
          attendanceRecords.push(attendanceRecord);
        }
      } catch (error) {
        errors.push({
          studentId: studentData.studentId,
          error: error.message
        });
      }
    }

    res.status(201).json({
      success: true,
      message: `Attendance marked for ${attendanceRecords.length} students`,
      data: {
        attendanceRecords: attendanceRecords.length,
        errors: errors
      }
    });

  } catch (error) {
    console.error('Mark attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while marking attendance'
    });
  }
});

// @desc    Get attendance for a course on a specific date
// @route   GET /api/attendance/course/:courseId
// @access  Private (Faculty)
router.get('/course/:courseId', [
  auth,
  authorize('faculty', 'admin'),
  query('date').isISO8601().withMessage('Valid date required'),
  query('sessionType').optional().isIn(['Lecture', 'Lab', 'Tutorial', 'Seminar', 'Exam'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { courseId } = req.params;
    const { date, sessionType } = req.query;

    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    let query = {
      course: courseId,
      date: { $gte: startOfDay, $lte: endOfDay }
    };

    if (sessionType) {
      query.sessionType = sessionType;
    }

    const attendance = await Attendance.find(query)
      .populate('student', 'name rollNumber email batch semester')
      .populate('markedBy', 'name designation')
      .sort({ 'student.rollNumber': 1 });

    // Get course details
    const course = await Course.findById(courseId)
      .populate('faculty', 'name designation');

    // Get all students enrolled in this course
    const enrolledStudents = await Student.find({
      isActive: true,
      // Add course enrollment logic here based on your schema
    }).select('name rollNumber email batch semester');

    // Create attendance summary
    const summary = await Attendance.getCourseSummary(courseId, date);

    res.json({
      success: true,
      data: {
        course,
        attendance,
        enrolledStudents,
        summary,
        date: date
      }
    });

  } catch (error) {
    console.error('Get course attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching attendance'
    });
  }
});

// @desc    Get student attendance summary
// @route   GET /api/attendance/student/:studentId
// @access  Private
router.get('/student/:studentId', [
  auth,
  query('academicYear').optional().matches(/^\d{4}-\d{2}$/).withMessage('Academic year must be in format YYYY-YY'),
  query('courseId').optional().isMongoId().withMessage('Valid course ID required')
], async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academicYear, courseId } = req.query;

    // Check if user can access this student's data
    if (req.user.role === 'student' && req.user.id !== studentId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    let query = { student: studentId };
    
    if (academicYear) {
      query.academicYear = academicYear;
    }
    
    if (courseId) {
      query.course = courseId;
    }

    const attendance = await Attendance.find(query)
      .populate('course', 'name code credits semester')
      .populate('markedBy', 'name designation')
      .sort({ date: -1 });

    // Calculate attendance percentage by course
    const courseStats = {};
    
    for (const record of attendance) {
      const courseKey = record.course._id.toString();
      
      if (!courseStats[courseKey]) {
        courseStats[courseKey] = {
          course: record.course,
          total: 0,
          present: 0,
          absent: 0,
          late: 0,
          excused: 0
        };
      }
      
      courseStats[courseKey].total++;
      courseStats[courseKey][record.status.toLowerCase()]++;
    }

    // Calculate percentages
    Object.keys(courseStats).forEach(courseKey => {
      const stats = courseStats[courseKey];
      stats.attendancePercentage = Math.round(
        ((stats.present + stats.late) / stats.total) * 100
      );
    });

    res.json({
      success: true,
      data: {
        attendance,
        courseStats: Object.values(courseStats),
        totalRecords: attendance.length
      }
    });

  } catch (error) {
    console.error('Get student attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching student attendance'
    });
  }
});

// @desc    Get attendance analytics
// @route   GET /api/attendance/analytics
// @access  Private (Faculty/Admin)
router.get('/analytics', [
  auth,
  authorize('faculty', 'admin'),
  query('startDate').isISO8601().withMessage('Valid start date required'),
  query('endDate').isISO8601().withMessage('Valid end date required'),
  query('courseId').optional().isMongoId().withMessage('Valid course ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { startDate, endDate, courseId } = req.query;

    let matchQuery = {
      date: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (courseId) {
      matchQuery.course = new mongoose.Types.ObjectId(courseId);
    }

    // Overall attendance statistics
    const overallStats = await Attendance.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Daily attendance trends
    const dailyTrends = await Attendance.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
            status: '$status'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          attendance: {
            $push: {
              status: '$_id.status',
              count: '$count'
            }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Course-wise attendance
    const courseWiseStats = await Attendance.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: {
            course: '$course',
            status: '$status'
          },
          count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'courses',
          localField: '_id.course',
          foreignField: '_id',
          as: 'courseInfo'
        }
      },
      {
        $group: {
          _id: '$_id.course',
          courseInfo: { $first: { $arrayElemAt: ['$courseInfo', 0] } },
          attendance: {
            $push: {
              status: '$_id.status',
              count: '$count'
            }
          }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        overallStats,
        dailyTrends,
        courseWiseStats,
        dateRange: { startDate, endDate }
      }
    });

  } catch (error) {
    console.error('Get attendance analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching analytics'
    });
  }
});

export default router;
