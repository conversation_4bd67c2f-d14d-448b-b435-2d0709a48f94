import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaBullhorn, FaTimes } from 'react-icons/fa';

const NewsTicker = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Sample news data - in real app, this would come from API
  const newsItems = [
    {
      id: 1,
      text: "🎉 New AI & Machine Learning Lab inaugurated with state-of-the-art equipment",
      type: "announcement",
      date: "2024-01-15"
    },
    {
      id: 2,
      text: "📚 Registration open for Spring 2024 semester - Last date: January 30th",
      type: "important",
      date: "2024-01-10"
    },
    {
      id: 3,
      text: "🏆 Our students won 1st place in National Hackathon 2024",
      type: "achievement",
      date: "2024-01-08"
    },
    {
      id: 4,
      text: "👨‍🏫 Guest lecture by Google Engineer on 'Future of Cloud Computing' - Jan 25th",
      type: "event",
      date: "2024-01-05"
    },
    {
      id: 5,
      text: "💼 Placement drive by Microsoft, Amazon, and Google starting February 1st",
      type: "placement",
      date: "2024-01-03"
    }
  ];

  // Auto-rotate news items
  useEffect(() => {
    if (newsItems.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prevIndex) => 
          prevIndex === newsItems.length - 1 ? 0 : prevIndex + 1
        );
      }, 5000); // Change every 5 seconds

      return () => clearInterval(interval);
    }
  }, [newsItems.length]);

  if (!isVisible || newsItems.length === 0) {
    return null;
  }

  const getTypeColor = (type) => {
    switch (type) {
      case 'important':
        return 'bg-red-600';
      case 'achievement':
        return 'bg-green-600';
      case 'event':
        return 'bg-purple-600';
      case 'placement':
        return 'bg-orange-600';
      default:
        return 'bg-blue-600';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      transition={{ duration: 0.5 }}
      className={`${getTypeColor(newsItems[currentIndex].type)} text-white py-2 relative overflow-hidden`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0">
            <div className="flex items-center mr-4 flex-shrink-0">
              <FaBullhorn className="text-lg mr-2" />
              <span className="font-semibold text-sm uppercase tracking-wide">
                Latest News
              </span>
            </div>
            
            <div className="flex-1 min-w-0">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
                className="text-sm md:text-base truncate"
              >
                {newsItems[currentIndex].text}
              </motion.div>
            </div>
          </div>

          {/* Navigation dots for multiple news items */}
          {newsItems.length > 1 && (
            <div className="flex items-center space-x-2 mr-4">
              {newsItems.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    index === currentIndex 
                      ? 'bg-white' 
                      : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                  }`}
                  aria-label={`Go to news item ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* Close button */}
          <button
            onClick={() => setIsVisible(false)}
            className="text-white hover:text-gray-200 transition-colors p-1"
            aria-label="Close news ticker"
          >
            <FaTimes className="text-sm" />
          </button>
        </div>
      </div>

      {/* Progress bar */}
      {newsItems.length > 1 && (
        <motion.div
          className="absolute bottom-0 left-0 h-0.5 bg-white bg-opacity-30"
          initial={{ width: '0%' }}
          animate={{ width: '100%' }}
          transition={{ duration: 5, ease: 'linear' }}
          key={currentIndex}
        />
      )}
    </motion.div>
  );
};

export default NewsTicker;
