# CS Department Website Demo 🎓

A comprehensive Computer Science Department website built with modern web technologies, featuring an attractive UI/UX with animations and all the essential features for an academic department.

## 🔥 Features

### 🏫 Core Features
- **Home Page** - Hero section with carousel, department stats, and featured content
- **About Department** - History, vision, mission, achievements, and timeline
- **Faculty Section** - Detailed faculty profiles with expertise and contact info
- **Student Achievements** - Hackathons, placements, research papers, and awards
- **Courses & Syllabus** - Semester-wise courses with PDF downloads and timetables
- **Events & Announcements** - Upcoming events, workshops, and news ticker
- **Labs & Facilities** - Detailed lab information with equipment and images
- **Placement & Alumni** - Statistics, company visits, and alumni testimonials
- **Gallery** - Photo gallery with categories and lightbox view
- **Contact Us** - Contact form, maps, and key personnel information

### 🌟 Advanced Features
- **Search Functionality** - Global search across all content with suggestions
- **AI Chatbot** - Interactive chatbot for department inquiries
- **Admin Dashboard** - Protected admin panel for content management
- **Responsive Design** - Mobile-first approach with beautiful animations
- **News Ticker** - Auto-updating announcements banner
- **Authentication** - JWT-based login system with role-based access

## 🛠 Tech Stack

### Frontend
- **React 18** with Vite for fast development
- **Tailwind CSS** for styling
- **Framer Motion** for smooth animations
- **React Router** for navigation
- **React Query** for data fetching
- **Swiper** for carousels
- **React Icons** for icons

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **Nodemailer** for email functionality
- **Express Rate Limiting** for security
- **Helmet** for security headers

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd demo
   ```

2. **Install Backend Dependencies**
   ```bash
   cd server
   npm install
   ```

3. **Install Frontend Dependencies**
   ```bash
   cd ../client
   npm install
   ```

4. **Environment Setup**
   ```bash
   cd ../server
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/cs_department
   JWT_SECRET=your_super_secret_jwt_key_here
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your_app_password
   ```

5. **Start MongoDB**
   ```bash
   # If using local MongoDB
   mongod
   ```

6. **Seed the Database**
   ```bash
   cd server
   npm run seed
   ```

7. **Start the Development Servers**
   
   **Backend (Terminal 1):**
   ```bash
   cd server
   npm run dev
   ```
   
   **Frontend (Terminal 2):**
   ```bash
   cd client
   npm run dev
   ```

8. **Access the Application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000

## 👥 Default Login Credentials

After seeding the database, you can use these credentials:

- **Admin**: <EMAIL> / admin123
- **Faculty**: <EMAIL> / faculty123
- **Student**: <EMAIL> / student123

## 📁 Project Structure

```
demo/
├── client/                 # React frontend
│   ├── public/            # Static assets
│   ├── src/
│   │   ├── components/    # Reusable components
│   │   │   ├── layout/    # Layout components
│   │   │   ├── sections/  # Page sections
│   │   │   └── ui/        # UI components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom hooks
│   │   ├── utils/         # Utility functions
│   │   └── styles/        # CSS files
│   └── package.json
├── server/                # Node.js backend
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   ├── middleware/       # Custom middleware
│   ├── scripts/          # Utility scripts
│   └── package.json
└── README.md
```

## 🎨 Key Components

### Frontend Components
- **Navbar** - Responsive navigation with search
- **Footer** - Comprehensive footer with links
- **Hero Carousel** - Animated image slider
- **Achievement Counter** - Animated statistics
- **News Ticker** - Scrolling announcements
- **Search Modal** - Global search interface
- **Chatbot** - Interactive AI assistant

### Backend Models
- **User** - Authentication and user management
- **Faculty** - Faculty profiles and information
- **Course** - Course details and syllabus
- **Event** - Events and workshops
- **Student** - Student profiles and achievements

## 🔧 Available Scripts

### Frontend (client/)
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
```

### Backend (server/)
```bash
npm run dev          # Start development server with nodemon
npm start            # Start production server
npm run seed         # Seed database with sample data
```

## 🌟 Features Showcase

### Animations & UX
- Smooth page transitions with Framer Motion
- Hover effects and micro-interactions
- Loading states and skeleton screens
- Responsive design for all devices

### Content Management
- Admin dashboard for managing content
- File upload for images and documents
- Rich text editing capabilities
- Bulk operations for data management

### Search & Discovery
- Global search with auto-suggestions
- Filter and sort functionality
- Category-based browsing
- Popular search terms

## 🔒 Security Features

- JWT-based authentication
- Rate limiting for API endpoints
- Input validation and sanitization
- CORS configuration
- Helmet for security headers
- Password hashing with bcrypt

## 📱 Responsive Design

The website is fully responsive and optimized for:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## 🚀 Deployment

### Frontend Deployment (Vercel/Netlify)
1. Build the project: `npm run build`
2. Deploy the `dist` folder

### Backend Deployment (Heroku/Railway)
1. Set environment variables
2. Deploy with: `git push heroku main`

### Database (MongoDB Atlas)
1. Create a MongoDB Atlas cluster
2. Update `MONGODB_URI` in environment variables

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]

---

**Built with ❤️ for Computer Science Education**
