import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaFlask, 
  FaMapMarkerAlt, 
  FaUsers,
  FaSearch,
  FaLaptop,
  FaCog,
  FaUser
} from 'react-icons/fa';

const LabsPage = () => {
  const [labs, setLabs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Sample labs data
  const sampleLabs = [
    {
      id: 1,
      name: "Artificial Intelligence Lab",
      description: "State-of-the-art AI lab equipped with high-performance GPUs and specialized software for machine learning research.",
      facilities: ["NVIDIA RTX 4090 GPUs", "TensorFlow/PyTorch", "Jupyter Notebooks", "Cloud Computing Access"],
      capacity: 40,
      location: "Block A, Floor 3",
      incharge: "Dr. <PERSON>",
      equipment: [
        { name: "High-Performance Workstations", count: 20 },
        { name: "NVIDIA RTX 4090 GPUs", count: 10 },
        { name: "AI Development Software", count: "Unlimited" }
      ],
      images: ["/images/labs/ai-lab-1.jpg", "/images/labs/ai-lab-2.jpg"],
      isActive: true
    },
    {
      id: 2,
      name: "Cybersecurity Lab",
      description: "Advanced cybersecurity lab with penetration testing tools and network security equipment.",
      facilities: ["Kali Linux", "Wireshark", "Metasploit", "Network Simulators", "Firewall Systems"],
      capacity: 30,
      location: "Block B, Floor 2",
      incharge: "Prof. Michael Chen",
      equipment: [
        { name: "Security Testing Workstations", count: 15 },
        { name: "Network Security Appliances", count: 5 },
        { name: "Penetration Testing Tools", count: "Multiple" }
      ],
      images: ["/images/labs/cyber-lab-1.jpg", "/images/labs/cyber-lab-2.jpg"],
      isActive: true
    },
    {
      id: 3,
      name: "IoT & Embedded Systems Lab",
      description: "Internet of Things lab with Arduino, Raspberry Pi, and various sensors for embedded system development.",
      facilities: ["Arduino Boards", "Raspberry Pi", "Sensors & Actuators", "3D Printers", "Oscilloscopes"],
      capacity: 35,
      location: "Block A, Floor 2",
      incharge: "Dr. Emily Rodriguez",
      equipment: [
        { name: "Arduino Development Kits", count: 25 },
        { name: "Raspberry Pi Boards", count: 20 },
        { name: "Various Sensors", count: "100+" },
        { name: "3D Printers", count: 3 }
      ],
      images: ["/images/labs/iot-lab-1.jpg", "/images/labs/iot-lab-2.jpg"],
      isActive: true
    },
    {
      id: 4,
      name: "Database Systems Lab",
      description: "Comprehensive database lab with enterprise-grade database management systems and analytics tools.",
      facilities: ["Oracle Database", "MySQL", "PostgreSQL", "MongoDB", "Data Analytics Tools"],
      capacity: 45,
      location: "Block B, Floor 1",
      incharge: "Prof. David Kumar",
      equipment: [
        { name: "Database Servers", count: 5 },
        { name: "Client Workstations", count: 30 },
        { name: "Enterprise Software Licenses", count: "Multiple" }
      ],
      images: ["/images/labs/db-lab-1.jpg", "/images/labs/db-lab-2.jpg"],
      isActive: true
    },
    {
      id: 5,
      name: "Software Engineering Lab",
      description: "Modern software development lab with latest IDEs, version control systems, and collaboration tools.",
      facilities: ["Visual Studio", "IntelliJ IDEA", "Git/GitHub", "Jenkins", "Docker", "Kubernetes"],
      capacity: 50,
      location: "Block A, Floor 1",
      incharge: "Dr. Lisa Wang",
      equipment: [
        { name: "Development Workstations", count: 35 },
        { name: "Collaboration Tools", count: "Cloud-based" },
        { name: "CI/CD Pipeline Tools", count: "Multiple" }
      ],
      images: ["/images/labs/se-lab-1.jpg", "/images/labs/se-lab-2.jpg"],
      isActive: true
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setLabs(sampleLabs);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredLabs = labs.filter(lab => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return lab.name.toLowerCase().includes(query) ||
           lab.description.toLowerCase().includes(query) ||
           lab.facilities.some(facility => facility.toLowerCase().includes(query)) ||
           lab.incharge.toLowerCase().includes(query);
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading labs...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Labs & Facilities
            </h1>
            <p className="text-xl md:text-2xl text-green-100 max-w-3xl mx-auto">
              State-of-the-art laboratories equipped with cutting-edge technology for hands-on learning
            </p>
          </motion.div>
        </div>
      </section>

      {/* Search */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search labs and facilities..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Labs Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredLabs.length === 0 ? (
            <div className="text-center py-12">
              <FaFlask className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No labs found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search criteria
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {filteredLabs.map((lab, index) => (
                <motion.div
                  key={lab.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden"
                >
                  {/* Lab Header */}
                  <div className="h-48 bg-gradient-to-br from-green-500 to-blue-600 relative">
                    <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                    <div className="absolute bottom-4 left-4 text-white">
                      <FaFlask className="text-4xl mb-2" />
                      <h3 className="text-2xl font-bold">{lab.name}</h3>
                    </div>
                  </div>

                  {/* Lab Content */}
                  <div className="p-6">
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {lab.description}
                    </p>

                    {/* Lab Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div className="flex items-center text-sm text-gray-600">
                        <FaMapMarkerAlt className="mr-2 text-green-500" />
                        <span>{lab.location}</span>
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <FaUsers className="mr-2 text-green-500" />
                        <span>Capacity: {lab.capacity} students</span>
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600 md:col-span-2">
                        <FaUser className="mr-2 text-green-500" />
                        <span>In-charge: {lab.incharge}</span>
                      </div>
                    </div>

                    {/* Facilities */}
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                        <FaCog className="mr-2 text-green-500" />
                        Key Facilities
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {lab.facilities.map((facility, idx) => (
                          <span
                            key={idx}
                            className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full"
                          >
                            {facility}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Equipment */}
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                        <FaLaptop className="mr-2 text-green-500" />
                        Equipment
                      </h4>
                      <div className="space-y-2">
                        {lab.equipment.map((item, idx) => (
                          <div key={idx} className="flex justify-between items-center text-sm">
                            <span className="text-gray-700">{item.name}</span>
                            <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                              {item.count}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Action Button */}
                    <button className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-md transition-colors duration-200 font-medium">
                      View Lab Details
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Lab Statistics */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Lab Statistics
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our comprehensive lab facilities support hands-on learning and research
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-green-600 mb-2">{labs.length}</div>
              <div className="text-gray-600">Total Labs</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-green-600 mb-2">
                {labs.reduce((sum, lab) => sum + lab.capacity, 0)}
              </div>
              <div className="text-gray-600">Total Capacity</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-green-600 mb-2">
                {Math.round(labs.reduce((sum, lab) => sum + lab.capacity, 0) / labs.length)}
              </div>
              <div className="text-gray-600">Avg. Capacity</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-green-600 mb-2">24/7</div>
              <div className="text-gray-600">Access Hours</div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LabsPage;
