import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaUser,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaGraduationCap,
  FaSearch,
  FaFilter,
  FaLinkedin,
  FaResearchgate
} from 'react-icons/fa';

const FacultyPage = () => {
  const [faculty, setFaculty] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSpecialization, setSelectedSpecialization] = useState('all');

  // Sample faculty data
  const sampleFaculty = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      designation: 'Professor & Head of Department',
      qualification: ['Ph.D. in Computer Science', 'M.Tech in Computer Science'],
      specialization: ['Artificial Intelligence', 'Machine Learning', 'Data Science'],
      experience: 18,
      bio: 'Dr. <PERSON> is a distinguished academician and researcher with over 18 years of experience in computer science education and research.',
      researchInterests: ['Machine Learning', 'Data Analytics', 'Computer Vision', 'Educational Technology'],
      contact: {
        email: '<EMAIL>',
        phone: '+91 80 1234 5680',
        office: 'Room 301, CS Block',
        officeHours: 'Mon-Fri 2:00-4:00 PM'
      },
      socialLinks: {
        linkedin: 'https://linkedin.com/in/krishnapriya',
        researchgate: 'https://researchgate.net/profile/krishna-priya'
      },
      photo: '/images/faculty/krishna-priya.jpg',
      displayOrder: 1
    },
    {
      id: 2,
      name: 'Ms. Jaithoon Bibi',
      designation: 'Assistant Professor',
      qualification: ['M.Tech in Computer Science', 'B.Tech in Computer Science'],
      specialization: ['Software Engineering', 'Database Management', 'Web Technologies'],
      experience: 8,
      bio: 'Ms. Jaithoon Bibi is a dedicated faculty member specializing in software engineering and database systems.',
      researchInterests: ['Software Engineering', 'Database Optimization', 'Web Development'],
      contact: {
        email: '<EMAIL>',
        phone: '+91 80 1234 5681',
        office: 'Room 302, CS Block',
        officeHours: 'Tue-Thu 3:00-5:00 PM'
      },
      socialLinks: {
        linkedin: 'https://linkedin.com/in/jaithoonbibi',
        researchgate: 'https://researchgate.net/profile/jaithoon-bibi'
      },
      photo: '/images/faculty/jaithoon-bibi.jpg',
      displayOrder: 2
    },
    {
      id: 3,
      name: 'Mr. Manoj',
      designation: 'Assistant Professor',
      qualification: ['M.Tech in Computer Science', 'B.E in Computer Science'],
      specialization: ['Computer Networks', 'Cybersecurity', 'System Administration'],
      experience: 10,
      bio: 'Mr. Manoj is an experienced faculty member with expertise in computer networks and cybersecurity.',
      researchInterests: ['Network Security', 'Wireless Networks', 'System Security'],
      contact: {
        email: '<EMAIL>',
        phone: '+91 80 1234 5682',
        office: 'Room 303, CS Block',
        officeHours: 'Mon-Wed 1:00-3:00 PM'
      },
      socialLinks: {
        linkedin: 'https://linkedin.com/in/manoj-cs',
        researchgate: 'https://researchgate.net/profile/manoj-cs'
      },
      photo: '/images/faculty/manoj.jpg',
      displayOrder: 3
    },
    {
      id: 4,
      name: 'Ms. Kavitha',
      designation: 'Assistant Professor',
      qualification: ['M.Tech in Computer Science', 'B.Tech in Information Technology'],
      specialization: ['Programming Languages', 'Algorithms', 'Data Structures'],
      experience: 6,
      bio: 'Ms. Kavitha is a passionate educator focusing on programming fundamentals and algorithmic thinking.',
      researchInterests: ['Algorithm Design', 'Programming Methodologies', 'Educational Research'],
      contact: {
        email: '<EMAIL>',
        phone: '+91 80 1234 5683',
        office: 'Room 304, CS Block',
        officeHours: 'Mon-Fri 10:00-12:00 PM'
      },
      socialLinks: {
        linkedin: 'https://linkedin.com/in/kavitha-cs',
        researchgate: 'https://researchgate.net/profile/kavitha-cs'
      },
      photo: '/images/faculty/kavitha.jpg',
      displayOrder: 4
    },
    {
      id: 5,
      name: 'Ms. Gowri',
      designation: 'Assistant Professor',
      qualification: ['M.Tech in Computer Science', 'B.Tech in Computer Science'],
      specialization: ['Mobile Computing', 'Human-Computer Interaction', 'UI/UX Design'],
      experience: 5,
      bio: 'Ms. Gowri is a young and dynamic faculty member specializing in mobile technologies and user experience design.',
      researchInterests: ['Mobile App Development', 'User Experience', 'Interface Design'],
      contact: {
        email: '<EMAIL>',
        phone: '+91 80 1234 5684',
        office: 'Room 305, CS Block',
        officeHours: 'Tue-Thu 11:00-1:00 PM'
      },
      socialLinks: {
        linkedin: 'https://linkedin.com/in/gowri-cs',
        researchgate: 'https://researchgate.net/profile/gowri-cs'
      },
      photo: '/images/faculty/gowri.jpg',
      displayOrder: 5
    }
  ];

  const specializations = [
    'Artificial Intelligence',
    'Machine Learning',
    'Data Science',
    'Software Engineering',
    'Database Management',
    'Web Technologies',
    'Computer Networks',
    'Cybersecurity',
    'Programming Languages',
    'Algorithms',
    'Mobile Computing',
    'Human-Computer Interaction'
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setFaculty(sampleFaculty);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredFaculty = faculty.filter(member => {
    const matchesSearch = !searchQuery ||
      member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.designation.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.specialization.some(spec => spec.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesSpecialization = selectedSpecialization === 'all' ||
      member.specialization.includes(selectedSpecialization);

    return matchesSearch && matchesSpecialization;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading faculty...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-purple-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Our Faculty
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Meet our distinguished faculty members who are experts in their respective fields
            </p>
          </motion.div>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search faculty..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Specialization Filter */}
            <div className="flex items-center space-x-4">
              <FaFilter className="text-gray-500" />
              <select
                value={selectedSpecialization}
                onChange={(e) => setSelectedSpecialization(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Specializations</option>
                {specializations.map(spec => (
                  <option key={spec} value={spec}>{spec}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Faculty Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredFaculty.length === 0 ? (
            <div className="text-center py-12">
              <FaUser className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No faculty found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredFaculty.map((member, index) => (
                <motion.div
                  key={member.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden"
                >
                  {/* Faculty Photo */}
                  <div className="h-64 bg-gradient-to-br from-blue-500 to-purple-600 relative">
                    <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                    <div className="absolute bottom-4 left-4 text-white">
                      <FaUser className="text-4xl mb-2" />
                    </div>
                  </div>

                  {/* Faculty Info */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800 mb-1">
                      {member.name}
                    </h3>
                    <p className="text-blue-600 font-semibold mb-3">
                      {member.designation}
                    </p>

                    {/* Qualifications */}
                    <div className="mb-4">
                      <h4 className="font-semibold text-gray-700 mb-2 flex items-center">
                        <FaGraduationCap className="mr-2 text-blue-500" />
                        Qualifications
                      </h4>
                      <div className="space-y-1">
                        {member.qualification.map((qual, idx) => (
                          <p key={idx} className="text-sm text-gray-600">{qual}</p>
                        ))}
                      </div>
                    </div>

                    {/* Specializations */}
                    <div className="mb-4">
                      <h4 className="font-semibold text-gray-700 mb-2">Specialization</h4>
                      <div className="flex flex-wrap gap-2">
                        {member.specialization.map((spec, idx) => (
                          <span
                            key={idx}
                            className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                          >
                            {spec}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Experience */}
                    <div className="mb-4">
                      <p className="text-sm text-gray-600">
                        <span className="font-semibold">Experience:</span> {member.experience} years
                      </p>
                    </div>

                    {/* Contact Info */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <FaEnvelope className="mr-2 text-blue-500" />
                        <span className="truncate">{member.contact.email}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <FaPhone className="mr-2 text-blue-500" />
                        <span>{member.contact.phone}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <FaMapMarkerAlt className="mr-2 text-blue-500" />
                        <span>{member.contact.office}</span>
                      </div>
                    </div>

                    {/* Office Hours */}
                    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm font-semibold text-gray-700 mb-1">Office Hours:</p>
                      <p className="text-sm text-gray-600">{member.contact.officeHours}</p>
                    </div>

                    {/* Social Links */}
                    <div className="flex space-x-3 mb-4">
                      <a
                        href={member.socialLinks.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 transition-colors"
                      >
                        <FaLinkedin className="text-xl" />
                      </a>
                      <a
                        href={member.socialLinks.researchgate}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-green-600 hover:text-green-800 transition-colors"
                      >
                        <FaResearchgate className="text-xl" />
                      </a>
                    </div>

                    {/* View Profile Button */}
                    <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors duration-200 font-medium">
                      View Full Profile
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default FacultyPage;
     