import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaCalendarAlt, 
  FaMapMarkerAlt, 
  FaClock, 
  FaUsers,
  FaArrowRight,
  FaExternalLinkAlt
} from 'react-icons/fa';

const FeaturedEvents = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  // Sample events data - in real app, this would come from API
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setEvents([
        {
          id: 1,
          title: "AI & Machine Learning Workshop",
          description: "Hands-on workshop covering latest trends in AI and ML with industry experts from Google and Microsoft.",
          date: "2024-02-15",
          time: "10:00 AM - 4:00 PM",
          venue: "AI Lab, Block A",
          type: "Workshop",
          image: "/images/events/ai-workshop.jpg",
          registrationRequired: true,
          maxParticipants: 50,
          currentParticipants: 35,
          speaker: {
            name: "<PERSON>. <PERSON>",
            designation: "Senior AI Researcher",
            organization: "Google AI"
          },
          tags: ["AI", "Machine Learning", "Workshop"],
          isFeatured: true
        },
        {
          id: 2,
          title: "National Hackathon 2024",
          description: "48-hour coding marathon with exciting prizes and opportunities to showcase your programming skills.",
          date: "2024-02-20",
          time: "9:00 AM",
          venue: "Main Auditorium",
          type: "Hackathon",
          image: "/images/events/hackathon.jpg",
          registrationRequired: true,
          maxParticipants: 200,
          currentParticipants: 150,
          tags: ["Hackathon", "Programming", "Competition"],
          isFeatured: true,
          prizes: ["₹50,000", "₹30,000", "₹20,000"]
        },
        {
          id: 3,
          title: "Industry Connect: Future of Cloud Computing",
          description: "Guest lecture by industry leaders discussing the evolution and future prospects of cloud technologies.",
          date: "2024-02-25",
          time: "2:00 PM - 4:00 PM",
          venue: "Seminar Hall 1",
          type: "Guest Lecture",
          image: "/images/events/cloud-computing.jpg",
          registrationRequired: false,
          speaker: {
            name: "Mr. Raj Patel",
            designation: "Cloud Solutions Architect",
            organization: "Amazon Web Services"
          },
          tags: ["Cloud Computing", "Industry", "Guest Lecture"],
          isFeatured: true
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getEventTypeColor = (type) => {
    switch (type.toLowerCase()) {
      case 'workshop':
        return 'bg-blue-100 text-blue-800';
      case 'hackathon':
        return 'bg-purple-100 text-purple-800';
      case 'guest lecture':
        return 'bg-green-100 text-green-800';
      case 'seminar':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const isEventUpcoming = (dateString) => {
    return new Date(dateString) > new Date();
  };

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
                <div className="h-48 bg-gray-300 rounded mb-4"></div>
                <div className="h-6 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded mb-4"></div>
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Featured Events
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Stay updated with our latest workshops, seminars, and academic events designed to enhance your learning experience.
          </p>
        </motion.div>

        {/* Events Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {events.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden group"
            >
              {/* Event Image */}
              <div className="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
                <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                <div className="absolute top-4 left-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getEventTypeColor(event.type)}`}>
                    {event.type}
                  </span>
                </div>
                {isEventUpcoming(event.date) && (
                  <div className="absolute top-4 right-4">
                    <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      Upcoming
                    </span>
                  </div>
                )}
              </div>

              {/* Event Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                  {event.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {event.description}
                </p>

                {/* Event Details */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-500">
                    <FaCalendarAlt className="mr-2 text-blue-500" />
                    <span>{formatDate(event.date)}</span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500">
                    <FaClock className="mr-2 text-blue-500" />
                    <span>{event.time}</span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500">
                    <FaMapMarkerAlt className="mr-2 text-blue-500" />
                    <span>{event.venue}</span>
                  </div>

                  {event.registrationRequired && (
                    <div className="flex items-center text-sm text-gray-500">
                      <FaUsers className="mr-2 text-blue-500" />
                      <span>
                        {event.currentParticipants}/{event.maxParticipants} registered
                      </span>
                    </div>
                  )}
                </div>

                {/* Speaker Info */}
                {event.speaker && (
                  <div className="bg-gray-50 rounded-lg p-3 mb-4">
                    <p className="text-sm font-semibold text-gray-800">{event.speaker.name}</p>
                    <p className="text-xs text-gray-600">
                      {event.speaker.designation}, {event.speaker.organization}
                    </p>
                  </div>
                )}

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {event.tags.map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Link
                    to={`/events/${event.id}`}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md transition-colors duration-200 text-sm font-medium"
                  >
                    View Details
                  </Link>
                  
                  {event.registrationRequired && isEventUpcoming(event.date) && (
                    <button className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm font-medium">
                      <FaExternalLinkAlt className="text-xs" />
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Events Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link
            to="/events"
            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-md transition-colors duration-200 font-medium"
          >
            View All Events
            <FaArrowRight className="ml-2" />
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturedEvents;
