import { useState } from 'react';
import { motion } from 'framer-motion';
import { FaEnvelope, FaLinkedin, FaSearch } from 'react-icons/fa';

const FacultyPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterArea, setFilterArea] = useState('');}
  
  const facultyMembers = [
    {
      id: 1,
      name: "Dr. <PERSON>",
      designation: "Professor & Head of Department",
      qualification: "Ph.D. in Computer Science, MIT",
      expertise: ["Artificial Intelligence", "Machine Learning", "Neural Networks"],
      image: "/images/faculty/faculty1.jpg",
      email: "<EMAIL>",
      linkedin: "https://linkedin.com/in/sarah<PERSON><PERSON><PERSON>",
    },
    {
      id: 2,
      name: "Dr. <PERSON>",
      designation: "Associate Professor",
      qualification: "Ph.D. in Computer Engineering, Stanford University",
      expertise: ["Computer Architecture", "VLSI Design", "Embedded Systems"],
      image: "/images/faculty/faculty2.jpg",
      email: "<EMAIL>",
      linkedin: "https://linkedin.com/in/micha<PERSON><PERSON>",
    },
    {
      id: 3,
      name: "Dr. <PERSON><PERSON>",
      designation: "Assistant Professor",
      qualification: "Ph.D. in Data Science, IIT Delhi",
      expertise: ["Big Data Analytics", "Data Mining", "Cloud Computing"],
      image: "/images/faculty/faculty3.jpg",}]
     