import express from 'express';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Sample gallery data - in real app, this would be from database
const galleryData = [
  {
    id: 1,
    title: "AI Workshop 2024",
    description: "Students participating in hands-on AI and Machine Learning workshop",
    category: "Workshop",
    date: "2024-01-15",
    images: [
      {
        url: "/images/gallery/ai-workshop-1.jpg",
        caption: "Students working on ML models",
        isPrimary: true
      },
      {
        url: "/images/gallery/ai-workshop-2.jpg",
        caption: "Expert demonstrating neural networks",
        isPrimary: false
      },
      {
        url: "/images/gallery/ai-workshop-3.jpg",
        caption: "Group discussion on AI ethics",
        isPrimary: false
      }
    ],
    tags: ["AI", "Machine Learning", "Workshop", "Students"],
    isPublished: true
  },
  {
    id: 2,
    title: "National Hackathon 2024",
    description: "48-hour coding marathon with innovative projects and solutions",
    category: "Competition",
    date: "2024-01-20",
    images: [
      {
        url: "/images/gallery/hackathon-1.jpg",
        caption: "Teams brainstorming innovative solutions",
        isPrimary: true
      },
      {
        url: "/images/gallery/hackathon-2.jpg",
        caption: "Intense coding sessions",
        isPrimary: false
      },
      {
        url: "/images/gallery/hackathon-3.jpg",
        caption: "Final presentations",
        isPrimary: false
      },
      {
        url: "/images/gallery/hackathon-4.jpg",
        caption: "Winners receiving awards",
        isPrimary: false
      }
    ],
    tags: ["Hackathon", "Competition", "Programming", "Innovation"],
    isPublished: true
  },
  {
    id: 3,
    title: "Lab Activities",
    description: "Students working in various computer science laboratories",
    category: "Academic",
    date: "2024-01-10",
    images: [
      {
        url: "/images/gallery/lab-1.jpg",
        caption: "Database lab session",
        isPrimary: true
      },
      {
        url: "/images/gallery/lab-2.jpg",
        caption: "IoT project development",
        isPrimary: false
      },
      {
        url: "/images/gallery/lab-3.jpg",
        caption: "Cybersecurity practical",
        isPrimary: false
      }
    ],
    tags: ["Labs", "Practical", "Learning", "Technology"],
    isPublished: true
  },
  {
    id: 4,
    title: "Industry Expert Lecture",
    description: "Guest lecture by industry professionals on emerging technologies",
    category: "Seminar",
    date: "2024-01-05",
    images: [
      {
        url: "/images/gallery/lecture-1.jpg",
        caption: "Expert sharing industry insights",
        isPrimary: true
      },
      {
        url: "/images/gallery/lecture-2.jpg",
        caption: "Interactive Q&A session",
        isPrimary: false
      }
    ],
    tags: ["Guest Lecture", "Industry", "Knowledge Sharing"],
    isPublished: true
  },
  {
    id: 5,
    title: "Cultural Fest 2024",
    description: "Annual cultural festival celebrating diversity and creativity",
    category: "Cultural",
    date: "2024-01-25",
    images: [
      {
        url: "/images/gallery/cultural-1.jpg",
        caption: "Traditional dance performance",
        isPrimary: true
      },
      {
        url: "/images/gallery/cultural-2.jpg",
        caption: "Music competition",
        isPrimary: false
      },
      {
        url: "/images/gallery/cultural-3.jpg",
        caption: "Art exhibition",
        isPrimary: false
      }
    ],
    tags: ["Cultural", "Festival", "Arts", "Celebration"],
    isPublished: true
  },
  {
    id: 6,
    title: "Graduation Ceremony 2023",
    description: "Celebrating the achievements of our graduating students",
    category: "Ceremony",
    date: "2023-12-15",
    images: [
      {
        url: "/images/gallery/graduation-1.jpg",
        caption: "Graduates receiving degrees",
        isPrimary: true
      },
      {
        url: "/images/gallery/graduation-2.jpg",
        caption: "Faculty congratulating students",
        isPrimary: false
      },
      {
        url: "/images/gallery/graduation-3.jpg",
        caption: "Group photo of graduates",
        isPrimary: false
      }
    ],
    tags: ["Graduation", "Achievement", "Ceremony", "Success"],
    isPublished: true
  }
];

// @desc    Get all gallery items
// @route   GET /api/gallery
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { 
      category, 
      tag, 
      search, 
      limit = 12, 
      page = 1,
      sortBy = 'date',
      sortOrder = 'desc'
    } = req.query;
    
    let filteredGallery = galleryData.filter(item => item.isPublished);
    
    // Filter by category
    if (category) {
      filteredGallery = filteredGallery.filter(item => 
        item.category.toLowerCase() === category.toLowerCase()
      );
    }
    
    // Filter by tag
    if (tag) {
      filteredGallery = filteredGallery.filter(item => 
        item.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
      );
    }
    
    // Search in title and description
    if (search) {
      const searchLower = search.toLowerCase();
      filteredGallery = filteredGallery.filter(item => 
        item.title.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    // Sort
    filteredGallery.sort((a, b) => {
      if (sortBy === 'date') {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
      } else if (sortBy === 'title') {
        return sortOrder === 'desc' 
          ? b.title.localeCompare(a.title)
          : a.title.localeCompare(b.title);
      }
      return 0;
    });
    
    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedGallery = filteredGallery.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: paginatedGallery,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(filteredGallery.length / parseInt(limit)),
        total: filteredGallery.length,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get gallery error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching gallery'
    });
  }
});

// @desc    Get single gallery item
// @route   GET /api/gallery/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const galleryId = parseInt(req.params.id);
    const galleryItem = galleryData.find(item => item.id === galleryId && item.isPublished);
    
    if (!galleryItem) {
      return res.status(404).json({
        success: false,
        message: 'Gallery item not found'
      });
    }
    
    res.json({
      success: true,
      data: galleryItem
    });
  } catch (error) {
    console.error('Get gallery item error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching gallery item'
    });
  }
});

// @desc    Get gallery categories
// @route   GET /api/gallery/categories/list
// @access  Public
router.get('/categories/list', async (req, res) => {
  try {
    const categories = [...new Set(galleryData
      .filter(item => item.isPublished)
      .map(item => item.category)
    )];
    
    const categoriesWithCount = categories.map(category => ({
      name: category,
      count: galleryData.filter(item => 
        item.isPublished && item.category === category
      ).length
    }));
    
    res.json({
      success: true,
      data: categoriesWithCount
    });
  } catch (error) {
    console.error('Get gallery categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching gallery categories'
    });
  }
});

// @desc    Get gallery tags
// @route   GET /api/gallery/tags/list
// @access  Public
router.get('/tags/list', async (req, res) => {
  try {
    const allTags = galleryData
      .filter(item => item.isPublished)
      .flatMap(item => item.tags);
    
    const tagCounts = allTags.reduce((acc, tag) => {
      acc[tag] = (acc[tag] || 0) + 1;
      return acc;
    }, {});
    
    const tagsWithCount = Object.entries(tagCounts)
      .map(([tag, count]) => ({ name: tag, count }))
      .sort((a, b) => b.count - a.count);
    
    res.json({
      success: true,
      data: tagsWithCount
    });
  } catch (error) {
    console.error('Get gallery tags error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching gallery tags'
    });
  }
});

// @desc    Get recent gallery items
// @route   GET /api/gallery/recent/items
// @access  Public
router.get('/recent/items', async (req, res) => {
  try {
    const { limit = 6 } = req.query;
    
    const recentItems = galleryData
      .filter(item => item.isPublished)
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, parseInt(limit))
      .map(item => ({
        id: item.id,
        title: item.title,
        category: item.category,
        date: item.date,
        primaryImage: item.images.find(img => img.isPrimary) || item.images[0],
        imageCount: item.images.length
      }));
    
    res.json({
      success: true,
      data: recentItems
    });
  } catch (error) {
    console.error('Get recent gallery items error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching recent gallery items'
    });
  }
});

export default router;
