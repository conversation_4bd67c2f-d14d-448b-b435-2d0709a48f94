import express from 'express';
import { body, validationResult } from 'express-validator';
import nodemailer from 'nodemailer';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting for contact form
const contactLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // limit each IP to 3 requests per windowMs
  message: {
    success: false,
    message: 'Too many contact form submissions, please try again later.'
  }
});

// Contact information
const contactInfo = {
  department: {
    name: "Department of Computer Science",
    address: {
      building: "Computer Science Block",
      street: "University Campus",
      city: "Bangalore",
      state: "Karnataka",
      pincode: "560001",
      country: "India"
    },
    phone: "+91 80 1234 5678",
    email: "<EMAIL>",
    fax: "+91 80 1234 5679"
  },
  office: {
    hours: {
      weekdays: "9:00 AM - 5:00 PM",
      saturday: "9:00 AM - 1:00 PM",
      sunday: "Closed"
    },
    location: "Room 101, CS Block"
  },
  coordinates: {
    latitude: 12.9716,
    longitude: 77.5946
  },
  socialMedia: {
    facebook: "https://facebook.com/csdept",
    twitter: "https://twitter.com/csdept",
    linkedin: "https://linkedin.com/company/csdept",
    instagram: "https://instagram.com/csdept"
  }
};

// Key contacts
const keyContacts = [
  {
    id: 1,
    name: "Dr. Rajesh Kumar",
    designation: "Head of Department",
    email: "<EMAIL>",
    phone: "+91 80 1234 5680",
    office: "Room 201, CS Block",
    photo: "/images/contacts/hod.jpg"
  },
  {
    id: 2,
    name: "Prof. Sunita Sharma",
    designation: "Academic Coordinator",
    email: "<EMAIL>",
    phone: "+91 80 1234 5681",
    office: "Room 202, CS Block",
    photo: "/images/contacts/academic.jpg"
  },
  {
    id: 3,
    name: "Dr. Amit Patel",
    designation: "Placement Officer",
    email: "<EMAIL>",
    phone: "+91 80 1234 5682",
    office: "Room 203, CS Block",
    photo: "/images/contacts/placement.jpg"
  },
  {
    id: 4,
    name: "Ms. Priya Singh",
    designation: "Administrative Officer",
    email: "<EMAIL>",
    phone: "+91 80 1234 5683",
    office: "Room 101, CS Block",
    photo: "/images/contacts/admin.jpg"
  }
];

// Create nodemailer transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// @desc    Get contact information
// @route   GET /api/contact/info
// @access  Public
router.get('/info', async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        department: contactInfo.department,
        office: contactInfo.office,
        coordinates: contactInfo.coordinates,
        socialMedia: contactInfo.socialMedia
      }
    });
  } catch (error) {
    console.error('Get contact info error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching contact information'
    });
  }
});

// @desc    Get key contacts
// @route   GET /api/contact/key-contacts
// @access  Public
router.get('/key-contacts', async (req, res) => {
  try {
    res.json({
      success: true,
      data: keyContacts
    });
  } catch (error) {
    console.error('Get key contacts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching key contacts'
    });
  }
});

// @desc    Submit contact form
// @route   POST /api/contact/submit
// @access  Public (with rate limiting)
router.post('/submit', [
  contactLimiter,
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('phone').optional().isMobilePhone('any').withMessage('Please provide a valid phone number'),
  body('subject').trim().isLength({ min: 5, max: 200 }).withMessage('Subject must be between 5 and 200 characters'),
  body('message').trim().isLength({ min: 10, max: 2000 }).withMessage('Message must be between 10 and 2000 characters'),
  body('category').isIn(['General Inquiry', 'Admission', 'Academic', 'Placement', 'Research', 'Other']).withMessage('Invalid category')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, email, phone, subject, message, category } = req.body;

    // Create email content
    const emailContent = `
      <h2>New Contact Form Submission</h2>
      <p><strong>Name:</strong> ${name}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
      <p><strong>Category:</strong> ${category}</p>
      <p><strong>Subject:</strong> ${subject}</p>
      <p><strong>Message:</strong></p>
      <p>${message.replace(/\n/g, '<br>')}</p>
      <hr>
      <p><small>Submitted on: ${new Date().toLocaleString()}</small></p>
    `;

    // Auto-reply content
    const autoReplyContent = `
      <h2>Thank you for contacting us!</h2>
      <p>Dear ${name},</p>
      <p>We have received your message and will get back to you within 24-48 hours.</p>
      <p><strong>Your message:</strong></p>
      <p><em>"${subject}"</em></p>
      <p>Best regards,<br>Department of Computer Science</p>
    `;

    try {
      const transporter = createTransporter();

      // Send email to department
      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: contactInfo.department.email,
        subject: `Contact Form: ${subject}`,
        html: emailContent
      });

      // Send auto-reply to user
      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: email,
        subject: 'Thank you for contacting CS Department',
        html: autoReplyContent
      });

      res.json({
        success: true,
        message: 'Your message has been sent successfully. We will get back to you soon.'
      });

    } catch (emailError) {
      console.error('Email sending error:', emailError);
      
      // Still return success to user, but log the error
      res.json({
        success: true,
        message: 'Your message has been received. We will get back to you soon.'
      });
    }

  } catch (error) {
    console.error('Contact form submission error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while processing your message. Please try again later.'
    });
  }
});

// @desc    Get contact form categories
// @route   GET /api/contact/categories
// @access  Public
router.get('/categories', async (req, res) => {
  try {
    const categories = [
      { value: 'General Inquiry', label: 'General Inquiry' },
      { value: 'Admission', label: 'Admission Related' },
      { value: 'Academic', label: 'Academic Information' },
      { value: 'Placement', label: 'Placement & Career' },
      { value: 'Research', label: 'Research Opportunities' },
      { value: 'Other', label: 'Other' }
    ];

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get contact categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching contact categories'
    });
  }
});

export default router;
