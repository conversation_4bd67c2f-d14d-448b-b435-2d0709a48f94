import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  FaSearch, 
  FaTimes, 
  FaUser, 
  FaBook, 
  FaCalendar, 
  FaFlask,
  FaBullhorn,
  FaArrowRight
} from 'react-icons/fa';

const SearchModal = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef(null);

  // Sample search data - in real app, this would come from API
  const searchData = [
    {
      id: 1,
      type: 'faculty',
      title: 'Dr. <PERSON>',
      description: 'Professor, AI and Machine Learning Expert',
      url: '/faculty/1',
      icon: FaU<PERSON>
    },
    {
      id: 2,
      type: 'course',
      title: 'CS101 - Introduction to Programming',
      description: 'Fundamental programming concepts using Python',
      url: '/courses/1',
      icon: FaBook
    },
    {
      id: 3,
      type: 'event',
      title: 'AI Workshop 2024',
      description: 'Hands-on workshop on Artificial Intelligence',
      url: '/events/1',
      icon: FaCalendar
    },
    {
      id: 4,
      type: 'lab',
      title: 'Artificial Intelligence Lab',
      description: 'State-of-the-art AI lab with high-performance GPUs',
      url: '/labs/1',
      icon: FaFlask
    },
    {
      id: 5,
      type: 'announcement',
      title: 'New AI Lab Inaugurated',
      description: 'State-of-the-art AI & Machine Learning Lab now open',
      url: '/announcements/1',
      icon: FaBullhorn
    }
  ];

  const popularSearches = [
    'AI and Machine Learning',
    'Faculty',
    'Courses',
    'Placement',
    'Labs',
    'Events',
    'Cybersecurity',
    'Database'
  ];

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Handle search
  useEffect(() => {
    if (query.trim().length > 0) {
      setLoading(true);
      
      // Simulate API call delay
      const timer = setTimeout(() => {
        const filteredResults = searchData.filter(item =>
          item.title.toLowerCase().includes(query.toLowerCase()) ||
          item.description.toLowerCase().includes(query.toLowerCase())
        );
        setResults(filteredResults);
        setLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setResults([]);
      setLoading(false);
    }
  }, [query]);

  // Handle suggestions
  useEffect(() => {
    if (query.trim().length > 0) {
      const filteredSuggestions = popularSearches.filter(search =>
        search.toLowerCase().includes(query.toLowerCase())
      ).slice(0, 5);
      setSuggestions(filteredSuggestions);
    } else {
      setSuggestions([]);
    }
  }, [query]);

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < results.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Enter' && selectedIndex >= 0) {
      e.preventDefault();
      // Navigate to selected result
      window.location.href = results[selectedIndex].url;
      onClose();
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'faculty': return 'text-blue-600 bg-blue-100';
      case 'course': return 'text-green-600 bg-green-100';
      case 'event': return 'text-purple-600 bg-purple-100';
      case 'lab': return 'text-orange-600 bg-orange-100';
      case 'announcement': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleResultClick = (url) => {
    onClose();
    // Navigate to result
    window.location.href = url;
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: -50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: -50 }}
            transition={{ duration: 0.2 }}
            className="fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl mx-4 bg-white rounded-lg shadow-2xl z-50 max-h-96 overflow-hidden"
          >
            {/* Search Input */}
            <div className="p-4 border-b border-gray-200">
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  ref={inputRef}
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Search faculty, courses, events, labs..."
                  className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                />
                <button
                  onClick={onClose}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <FaTimes />
                </button>
              </div>
            </div>

            {/* Search Results */}
            <div className="max-h-80 overflow-y-auto">
              {loading && (
                <div className="p-4 text-center">
                  <div className="spinner mx-auto"></div>
                  <p className="text-gray-500 mt-2">Searching...</p>
                </div>
              )}

              {!loading && query && results.length > 0 && (
                <div className="p-2">
                  <h3 className="text-sm font-semibold text-gray-500 px-3 py-2">
                    Search Results ({results.length})
                  </h3>
                  {results.map((result, index) => {
                    const IconComponent = result.icon;
                    return (
                      <motion.div
                        key={result.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className={`p-3 mx-2 rounded-md cursor-pointer transition-colors ${
                          selectedIndex === index 
                            ? 'bg-blue-50 border-l-4 border-blue-500' 
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handleResultClick(result.url)}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`p-2 rounded-full ${getTypeColor(result.type)}`}>
                            <IconComponent className="text-sm" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {result.title}
                            </h4>
                            <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                              {result.description}
                            </p>
                            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-2 ${getTypeColor(result.type)}`}>
                              {result.type}
                            </span>
                          </div>
                          <FaArrowRight className="text-gray-400 text-xs mt-1" />
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              )}

              {!loading && query && results.length === 0 && (
                <div className="p-8 text-center">
                  <FaSearch className="text-4xl text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No results found
                  </h3>
                  <p className="text-gray-500">
                    Try searching with different keywords
                  </p>
                </div>
              )}

              {!query && (
                <div className="p-4">
                  <h3 className="text-sm font-semibold text-gray-500 mb-3">
                    Popular Searches
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {popularSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => setQuery(search)}
                        className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors"
                      >
                        {search}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {suggestions.length > 0 && query && (
                <div className="border-t border-gray-200 p-4">
                  <h3 className="text-sm font-semibold text-gray-500 mb-2">
                    Suggestions
                  </h3>
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => setQuery(suggestion)}
                      className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      <FaSearch className="inline mr-2 text-gray-400" />
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default SearchModal;
