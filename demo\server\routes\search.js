import express from 'express';
import { optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Sample search data - in real app, this would search across multiple collections
const searchData = {
  faculty: [
    {
      id: 1,
      type: 'faculty',
      title: 'Dr. <PERSON>',
      description: 'Professor, AI and Machine Learning Expert',
      url: '/faculty/1',
      tags: ['AI', 'Machine Learning', 'Deep Learning', 'Neural Networks']
    },
    {
      id: 2,
      type: 'faculty',
      title: 'Prof. <PERSON>',
      description: 'Associate Professor, Cybersecurity Specialist',
      url: '/faculty/2',
      tags: ['Cybersecurity', 'Network Security', 'Cryptography']
    }
  ],
  courses: [
    {
      id: 1,
      type: 'course',
      title: 'CS101 - Introduction to Programming',
      description: 'Fundamental programming concepts using Python',
      url: '/courses/1',
      tags: ['Programming', 'Python', 'Fundamentals', 'CS101']
    },
    {
      id: 2,
      type: 'course',
      title: 'CS301 - Artificial Intelligence',
      description: 'Advanced AI concepts and machine learning algorithms',
      url: '/courses/2',
      tags: ['AI', 'Machine Learning', 'Algorithms', 'CS301']
    },
    {
      id: 3,
      type: 'course',
      title: 'CS401 - Database Management Systems',
      description: 'Comprehensive study of database design and management',
      url: '/courses/3',
      tags: ['Database', 'SQL', 'DBMS', 'CS401']
    }
  ],
  events: [
    {
      id: 1,
      type: 'event',
      title: 'AI Workshop 2024',
      description: 'Hands-on workshop on Artificial Intelligence and Machine Learning',
      url: '/events/1',
      tags: ['Workshop', 'AI', 'Machine Learning', '2024']
    },
    {
      id: 2,
      type: 'event',
      title: 'National Hackathon 2024',
      description: '48-hour coding marathon with exciting prizes',
      url: '/events/2',
      tags: ['Hackathon', 'Programming', 'Competition', '2024']
    }
  ],
  labs: [
    {
      id: 1,
      type: 'lab',
      title: 'Artificial Intelligence Lab',
      description: 'State-of-the-art AI lab with high-performance GPUs',
      url: '/labs/1',
      tags: ['AI', 'Lab', 'GPU', 'Research']
    },
    {
      id: 2,
      type: 'lab',
      title: 'Cybersecurity Lab',
      description: 'Advanced cybersecurity lab with penetration testing tools',
      url: '/labs/2',
      tags: ['Cybersecurity', 'Lab', 'Penetration Testing', 'Security']
    }
  ],
  announcements: [
    {
      id: 1,
      type: 'announcement',
      title: 'New AI Lab Inaugurated',
      description: 'State-of-the-art AI & Machine Learning Lab now open',
      url: '/announcements/1',
      tags: ['AI', 'Lab', 'Inauguration', 'News']
    }
  ]
};

// @desc    Global search
// @route   GET /api/search
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { 
      q: query, 
      type, 
      limit = 20,
      page = 1
    } = req.query;
    
    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters long'
      });
    }
    
    const searchTerm = query.toLowerCase().trim();
    let results = [];
    
    // Determine which types to search
    const typesToSearch = type ? [type] : Object.keys(searchData);
    
    // Search across specified types
    typesToSearch.forEach(searchType => {
      if (searchData[searchType]) {
        const typeResults = searchData[searchType].filter(item => {
          const titleMatch = item.title.toLowerCase().includes(searchTerm);
          const descriptionMatch = item.description.toLowerCase().includes(searchTerm);
          const tagMatch = item.tags.some(tag => tag.toLowerCase().includes(searchTerm));
          
          return titleMatch || descriptionMatch || tagMatch;
        });
        
        // Add relevance score
        const scoredResults = typeResults.map(item => {
          let score = 0;
          
          // Title exact match gets highest score
          if (item.title.toLowerCase() === searchTerm) score += 100;
          else if (item.title.toLowerCase().includes(searchTerm)) score += 50;
          
          // Description match
          if (item.description.toLowerCase().includes(searchTerm)) score += 20;
          
          // Tag matches
          item.tags.forEach(tag => {
            if (tag.toLowerCase() === searchTerm) score += 30;
            else if (tag.toLowerCase().includes(searchTerm)) score += 10;
          });
          
          return { ...item, relevanceScore: score };
        });
        
        results = results.concat(scoredResults);
      }
    });
    
    // Sort by relevance score
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    
    // Remove relevance score from final results
    results = results.map(({ relevanceScore, ...item }) => item);
    
    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedResults = results.slice(startIndex, endIndex);
    
    // Group results by type for better presentation
    const groupedResults = paginatedResults.reduce((acc, item) => {
      if (!acc[item.type]) {
        acc[item.type] = [];
      }
      acc[item.type].push(item);
      return acc;
    }, {});
    
    res.json({
      success: true,
      data: {
        query: query,
        results: paginatedResults,
        groupedResults,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(results.length / parseInt(limit)),
          total: results.length,
          limit: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while performing search'
    });
  }
});

// @desc    Get search suggestions
// @route   GET /api/search/suggestions
// @access  Public
router.get('/suggestions', async (req, res) => {
  try {
    const { q: query, limit = 5 } = req.query;
    
    if (!query || query.trim().length < 1) {
      return res.json({
        success: true,
        data: []
      });
    }
    
    const searchTerm = query.toLowerCase().trim();
    let suggestions = [];
    
    // Collect all titles and tags for suggestions
    Object.values(searchData).forEach(typeData => {
      typeData.forEach(item => {
        // Add title as suggestion
        if (item.title.toLowerCase().includes(searchTerm)) {
          suggestions.push({
            text: item.title,
            type: item.type,
            url: item.url
          });
        }
        
        // Add matching tags as suggestions
        item.tags.forEach(tag => {
          if (tag.toLowerCase().includes(searchTerm) && 
              !suggestions.some(s => s.text.toLowerCase() === tag.toLowerCase())) {
            suggestions.push({
              text: tag,
              type: 'tag',
              url: `/search?q=${encodeURIComponent(tag)}`
            });
          }
        });
      });
    });
    
    // Remove duplicates and limit results
    const uniqueSuggestions = suggestions
      .filter((suggestion, index, self) => 
        index === self.findIndex(s => s.text.toLowerCase() === suggestion.text.toLowerCase())
      )
      .slice(0, parseInt(limit));
    
    res.json({
      success: true,
      data: uniqueSuggestions
    });
  } catch (error) {
    console.error('Search suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching search suggestions'
    });
  }
});

// @desc    Get popular search terms
// @route   GET /api/search/popular
// @access  Public
router.get('/popular', async (req, res) => {
  try {
    // In a real app, this would be based on actual search analytics
    const popularTerms = [
      { term: 'AI', count: 150, category: 'Technology' },
      { term: 'Machine Learning', count: 120, category: 'Technology' },
      { term: 'Cybersecurity', count: 100, category: 'Technology' },
      { term: 'Database', count: 90, category: 'Technology' },
      { term: 'Programming', count: 85, category: 'Technology' },
      { term: 'Hackathon', count: 75, category: 'Events' },
      { term: 'Placement', count: 70, category: 'Career' },
      { term: 'Faculty', count: 65, category: 'People' },
      { term: 'Labs', count: 60, category: 'Facilities' },
      { term: 'Courses', count: 55, category: 'Academic' }
    ];
    
    res.json({
      success: true,
      data: popularTerms
    });
  } catch (error) {
    console.error('Popular search terms error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching popular search terms'
    });
  }
});

// @desc    Get search categories
// @route   GET /api/search/categories
// @access  Public
router.get('/categories', async (req, res) => {
  try {
    const categories = [
      { value: 'faculty', label: 'Faculty', icon: 'FaUsers', count: searchData.faculty.length },
      { value: 'courses', label: 'Courses', icon: 'FaBook', count: searchData.courses.length },
      { value: 'events', label: 'Events', icon: 'FaCalendar', count: searchData.events.length },
      { value: 'labs', label: 'Labs', icon: 'FaFlask', count: searchData.labs.length },
      { value: 'announcements', label: 'Announcements', icon: 'FaBullhorn', count: searchData.announcements.length }
    ];
    
    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Search categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching search categories'
    });
  }
});

export default router;
