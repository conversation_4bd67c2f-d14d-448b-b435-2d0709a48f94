import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaBriefcase, 
  FaTrophy, 
  FaUsers, 
  FaChartLine,
  FaBuilding,
  FaGraduationCap,
  FaLinkedin,
  FaQuoteLeft
} from 'react-icons/fa';

const PlacementPage = () => {
  const [placementStats, setPlacementStats] = useState(null);
  const [companies, setCompanies] = useState([]);
  const [alumni, setAlumni] = useState([]);
  const [loading, setLoading] = useState(true);

  // Sample placement statistics
  const sampleStats = {
    currentYear: 2024,
    totalStudents: 120,
    placedStudents: 115,
    placementPercentage: 95.8,
    averagePackage: 8.5,
    highestPackage: 45.0,
    medianPackage: 7.2
  };

  // Sample companies
  const sampleCompanies = [
    {
      id: 1,
      name: "Google",
      logo: "/images/companies/google.png",
      studentsPlaced: 8,
      averagePackage: 35.0,
      roles: ["Software Engineer", "Data Scientist", "Product Manager"]
    },
    {
      id: 2,
      name: "Microsoft",
      logo: "/images/companies/microsoft.png",
      studentsPlaced: 12,
      averagePackage: 28.0,
      roles: ["Software Engineer", "Cloud Engineer", "AI Engineer"]
    },
    {
      id: 3,
      name: "Amazon",
      logo: "/images/companies/amazon.png",
      studentsPlaced: 15,
      averagePackage: 22.0,
      roles: ["Software Engineer", "DevOps Engineer", "Data Engineer"]
    },
    {
      id: 4,
      name: "Meta",
      logo: "/images/companies/meta.png",
      studentsPlaced: 6,
      averagePackage: 32.0,
      roles: ["Software Engineer", "ML Engineer", "Frontend Engineer"]
    },
    {
      id: 5,
      name: "Apple",
      logo: "/images/companies/apple.png",
      studentsPlaced: 4,
      averagePackage: 38.0,
      roles: ["iOS Developer", "Software Engineer", "Hardware Engineer"]
    }
  ];

  // Sample alumni testimonials
  const sampleAlumni = [
    {
      id: 1,
      name: "Rahul Sharma",
      batch: "2020",
      currentPosition: "Senior Software Engineer",
      company: "Google",
      location: "Bangalore",
      photo: "/images/alumni/rahul.jpg",
      testimonial: "The CS department provided me with a strong foundation in computer science. The faculty's guidance and the practical exposure through projects helped me land my dream job at Google.",
      linkedIn: "https://linkedin.com/in/rahulsharma"
    },
    {
      id: 2,
      name: "Priya Patel",
      batch: "2019",
      currentPosition: "Principal Data Scientist",
      company: "Microsoft",
      location: "Seattle, USA",
      photo: "/images/alumni/priya.jpg",
      testimonial: "The research opportunities and mentorship I received during my time in the department were invaluable. It shaped my career in data science and AI.",
      linkedIn: "https://linkedin.com/in/priyapatel"
    },
    {
      id: 3,
      name: "Arjun Kumar",
      batch: "2021",
      currentPosition: "Product Manager",
      company: "Amazon",
      location: "Hyderabad",
      photo: "/images/alumni/arjun.jpg",
      testimonial: "The department's emphasis on both technical skills and soft skills prepared me well for a product management role. The industry connections were also very helpful.",
      linkedIn: "https://linkedin.com/in/arjunkumar"
    }
  ];

  useEffect(() => {
    // Simulate API calls
    setTimeout(() => {
      setPlacementStats(sampleStats);
      setCompanies(sampleCompanies);
      setAlumni(sampleAlumni);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading placement data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Placements & Alumni
            </h1>
            <p className="text-xl md:text-2xl text-green-100 max-w-3xl mx-auto">
              Exceptional placement record with top companies and successful alumni worldwide
            </p>
          </motion.div>
        </div>
      </section>

      {/* Placement Statistics */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Placement Statistics {placementStats.currentYear}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Outstanding placement record demonstrating the quality of our education and industry readiness of our students
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center p-6 bg-gradient-to-br from-green-500 to-blue-600 text-white rounded-lg"
            >
              <FaChartLine className="text-4xl mx-auto mb-4" />
              <div className="text-3xl font-bold mb-2">{placementStats.placementPercentage}%</div>
              <div className="text-green-100">Placement Rate</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center p-6 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-lg"
            >
              <FaBriefcase className="text-4xl mx-auto mb-4" />
              <div className="text-3xl font-bold mb-2">₹{placementStats.averagePackage} LPA</div>
              <div className="text-blue-100">Average Package</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center p-6 bg-gradient-to-br from-purple-500 to-pink-600 text-white rounded-lg"
            >
              <FaTrophy className="text-4xl mx-auto mb-4" />
              <div className="text-3xl font-bold mb-2">₹{placementStats.highestPackage} LPA</div>
              <div className="text-purple-100">Highest Package</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-center p-6 bg-gradient-to-br from-orange-500 to-red-600 text-white rounded-lg"
            >
              <FaUsers className="text-4xl mx-auto mb-4" />
              <div className="text-3xl font-bold mb-2">{placementStats.placedStudents}/{placementStats.totalStudents}</div>
              <div className="text-orange-100">Students Placed</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Top Recruiting Companies */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Top Recruiting Companies
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Leading global companies trust our graduates and regularly visit our campus for recruitment
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {companies.map((company, index) => (
              <motion.div
                key={company.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 p-6"
              >
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg mr-4 flex items-center justify-center">
                    <FaBuilding className="text-2xl text-gray-500" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">{company.name}</h3>
                    <p className="text-gray-600">₹{company.averagePackage} LPA avg.</p>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-semibold">{company.studentsPlaced}</span> students placed
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {company.roles.map((role, idx) => (
                      <span
                        key={idx}
                        className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                      >
                        {role}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Alumni Testimonials */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Alumni Success Stories
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Hear from our successful alumni who are making their mark in the industry
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {alumni.map((alum, index) => (
              <motion.div
                key={alum.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 p-6"
              >
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full mr-4 flex items-center justify-center">
                    <FaGraduationCap className="text-2xl text-gray-500" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">{alum.name}</h3>
                    <p className="text-blue-600 font-semibold">{alum.currentPosition}</p>
                    <p className="text-gray-600 text-sm">{alum.company} • {alum.location}</p>
                    <p className="text-gray-500 text-xs">Batch {alum.batch}</p>
                  </div>
                </div>

                <div className="mb-4">
                  <FaQuoteLeft className="text-2xl text-gray-300 mb-2" />
                  <p className="text-gray-600 text-sm italic leading-relaxed">
                    "{alum.testimonial}"
                  </p>
                </div>

                <div className="flex justify-between items-center">
                  <a
                    href={alum.linkedIn}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    <FaLinkedin className="text-xl" />
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default PlacementPage;
