import express from 'express';
import { body, validationResult } from 'express-validator';
import Faculty from '../models/Faculty.js';
import { protect, authorize, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// @desc    Get all faculty members
// @route   GET /api/faculty
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, designation, search, sortBy = 'displayOrder' } = req.query;
    
    // Build query
    let query = { isActive: true };
    
    if (designation) {
      query.designation = designation;
    }
    
    if (search) {
      query.$text = { $search: search };
    }
    
    // Build sort object
    let sort = {};
    switch (sortBy) {
      case 'name':
        sort = { name: 1 };
        break;
      case 'experience':
        sort = { experience: -1 };
        break;
      case 'designation':
        sort = { designation: 1, displayOrder: 1 };
        break;
      default:
        sort = { displayOrder: 1, name: 1 };
    }
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort,
      populate: {
        path: 'courses',
        select: 'name code semester'
      }
    };
    
    const faculty = await Faculty.find(query)
      .populate(options.populate)
      .sort(options.sort)
      .limit(options.limit * 1)
      .skip((options.page - 1) * options.limit);
    
    const total = await Faculty.countDocuments(query);
    
    res.json({
      success: true,
      data: faculty,
      pagination: {
        current: options.page,
        pages: Math.ceil(total / options.limit),
        total,
        limit: options.limit
      }
    });
  } catch (error) {
    console.error('Get faculty error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching faculty'
    });
  }
});

// @desc    Get single faculty member
// @route   GET /api/faculty/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const faculty = await Faculty.findById(req.params.id)
      .populate('courses', 'name code semester credits');
    
    if (!faculty) {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not found'
      });
    }
    
    if (!faculty.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not available'
      });
    }
    
    res.json({
      success: true,
      data: faculty
    });
  } catch (error) {
    console.error('Get faculty by ID error:', error);
    
    if (error.name === 'CastError') {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while fetching faculty member'
    });
  }
});

// @desc    Create new faculty member
// @route   POST /api/faculty
// @access  Private (Admin only)
router.post('/', [
  protect,
  authorize('admin'),
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('designation').isIn(['Professor', 'Associate Professor', 'Assistant Professor', 'Lecturer', 'Head of Department']).withMessage('Invalid designation'),
  body('qualification').isArray({ min: 1 }).withMessage('At least one qualification is required'),
  body('specialization').isArray({ min: 1 }).withMessage('At least one specialization is required'),
  body('experience').isNumeric().isInt({ min: 0 }).withMessage('Experience must be a positive number')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    // Check if faculty with email already exists
    const existingFaculty = await Faculty.findOne({ email: req.body.email });
    if (existingFaculty) {
      return res.status(400).json({
        success: false,
        message: 'Faculty member with this email already exists'
      });
    }
    
    const faculty = await Faculty.create(req.body);
    
    res.status(201).json({
      success: true,
      message: 'Faculty member created successfully',
      data: faculty
    });
  } catch (error) {
    console.error('Create faculty error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating faculty member'
    });
  }
});

// @desc    Update faculty member
// @route   PUT /api/faculty/:id
// @access  Private (Admin only)
router.put('/:id', [
  protect,
  authorize('admin'),
  body('name').optional().trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('designation').optional().isIn(['Professor', 'Associate Professor', 'Assistant Professor', 'Lecturer', 'Head of Department']).withMessage('Invalid designation'),
  body('experience').optional().isNumeric().isInt({ min: 0 }).withMessage('Experience must be a positive number')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const faculty = await Faculty.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!faculty) {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Faculty member updated successfully',
      data: faculty
    });
  } catch (error) {
    console.error('Update faculty error:', error);
    
    if (error.name === 'CastError') {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while updating faculty member'
    });
  }
});

// @desc    Delete faculty member
// @route   DELETE /api/faculty/:id
// @access  Private (Admin only)
router.delete('/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const faculty = await Faculty.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );
    
    if (!faculty) {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Faculty member deactivated successfully'
    });
  } catch (error) {
    console.error('Delete faculty error:', error);
    
    if (error.name === 'CastError') {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while deleting faculty member'
    });
  }
});

export default router;
