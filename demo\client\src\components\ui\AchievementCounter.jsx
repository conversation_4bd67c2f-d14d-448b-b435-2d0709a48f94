import { useState, useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';

const AchievementCounter = ({ icon, count, title, description, duration = 2000, suffix = '' }) => {
  const [currentCount, setCurrentCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  useEffect(() => {
    if (isInView && !hasAnimated) {
      setHasAnimated(true);
      
      const startTime = Date.now();
      const endTime = startTime + duration;
      
      const updateCount = () => {
        const now = Date.now();
        const progress = Math.min((now - startTime) / duration, 1);
        
        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(easeOutQuart * count);
        
        setCurrentCount(currentValue);
        
        if (progress < 1) {
          requestAnimationFrame(updateCount);
        } else {
          setCurrentCount(count);
        }
      };
      
      requestAnimationFrame(updateCount);
    }
  }, [isInView, count, duration, hasAnimated]);

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
    >
      {/* Icon */}
      <motion.div
        initial={{ scale: 0 }}
        whileInView={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        viewport={{ once: true }}
        className="flex justify-center mb-4"
      >
        {icon}
      </motion.div>

      {/* Counter */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        viewport={{ once: true }}
        className="mb-2"
      >
        <span className="text-4xl md:text-5xl font-bold text-gray-800">
          {formatNumber(currentCount)}
          {suffix && <span className="text-2xl md:text-3xl">{suffix}</span>}
        </span>
      </motion.div>

      {/* Title */}
      <motion.h3
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        viewport={{ once: true }}
        className="text-xl font-semibold text-gray-800 mb-2"
      >
        {title}
      </motion.h3>

      {/* Description */}
      <motion.p
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        viewport={{ once: true }}
        className="text-gray-600 text-sm leading-relaxed"
      >
        {description}
      </motion.p>

      {/* Decorative element */}
      <motion.div
        initial={{ width: 0 }}
        whileInView={{ width: '50%' }}
        transition={{ duration: 0.8, delay: 1 }}
        viewport={{ once: true }}
        className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-4 rounded-full"
      />
    </motion.div>
  );
};

export default AchievementCounter;
