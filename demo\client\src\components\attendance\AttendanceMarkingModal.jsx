import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaTimes, 
  FaUserCheck, 
  FaUserTimes, 
  FaClock, 
  FaExclamationTriangle,
  FaSave,
  FaUsers
} from 'react-icons/fa';

const AttendanceMarkingModal = ({ isOpen, onClose, course, date, sessionType, students = [] }) => {
  const [attendanceData, setAttendanceData] = useState({});
  const [location, setLocation] = useState('');
  const [duration, setDuration] = useState(60);
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectAll, setSelectAll] = useState('Present');

  // Sample students data if not provided
  const sampleStudents = [
    { id: 1, name: '<PERSON><PERSON>ha <PERSON>', rollNumber: 'CSCS2024001', batch: '2024' },
    { id: 2, name: '<PERSON><PERSON><PERSON>', rollNumber: 'CSCS2024002', batch: '2024' },
    { id: 3, name: 'Sambavi <PERSON>', rollNumber: 'CSCS2024003', batch: '2024' },
    { id: 4, name: 'Thirsadha Saraal J', rollNumber: 'CSCS2024004', batch: '2024' },
    { id: 5, name: 'Rahul Kumar', rollNumber: 'CSCS2024005', batch: '2024' },
    { id: 6, name: 'Priya Sharma', rollNumber: 'CSCS2024006', batch: '2024' },
    { id: 7, name: 'Arjun Patel', rollNumber: 'CSCS2024007', batch: '2024' },
    { id: 8, name: 'Sneha Singh', rollNumber: 'CSCS2024008', batch: '2024' }
  ];

  const studentList = students.length > 0 ? students : sampleStudents;

  useEffect(() => {
    if (isOpen) {
      // Initialize attendance data with default 'Present' status
      const initialData = {};
      studentList.forEach(student => {
        initialData[student.id] = {
          status: 'Present',
          notes: ''
        };
      });
      setAttendanceData(initialData);
      setLocation(`${sessionType} Hall - CS Block`);
    }
  }, [isOpen, studentList, sessionType]);

  const statusOptions = [
    { value: 'Present', label: 'Present', icon: FaUserCheck, color: 'text-green-600 bg-green-100' },
    { value: 'Absent', label: 'Absent', icon: FaUserTimes, color: 'text-red-600 bg-red-100' },
    { value: 'Late', label: 'Late', icon: FaClock, color: 'text-yellow-600 bg-yellow-100' },
    { value: 'Excused', label: 'Excused', icon: FaExclamationTriangle, color: 'text-blue-600 bg-blue-100' }
  ];

  const handleStatusChange = (studentId, status) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        status: status
      }
    }));
  };

  const handleNotesChange = (studentId, notes) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        notes: notes
      }
    }));
  };

  const handleSelectAll = (status) => {
    const updatedData = {};
    studentList.forEach(student => {
      updatedData[student.id] = {
        ...attendanceData[student.id],
        status: status
      };
    });
    setAttendanceData(updatedData);
    setSelectAll(status);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare data for API
      const attendanceRecords = studentList.map(student => ({
        studentId: student.id,
        status: attendanceData[student.id]?.status || 'Present',
        notes: attendanceData[student.id]?.notes || ''
      }));

      const payload = {
        students: attendanceRecords,
        courseId: course?.id,
        date: date,
        sessionType: sessionType,
        location: location,
        duration: duration,
        notes: notes
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Attendance marked:', payload);
      
      // Close modal and show success message
      onClose();
      alert('Attendance marked successfully!');
      
    } catch (error) {
      console.error('Error marking attendance:', error);
      alert('Error marking attendance. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusCounts = () => {
    const counts = { Present: 0, Absent: 0, Late: 0, Excused: 0 };
    Object.values(attendanceData).forEach(data => {
      counts[data.status]++;
    });
    return counts;
  };

  const statusCounts = getStatusCounts();

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-blue-600 text-white px-6 py-4 flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold">Mark Attendance</h2>
              <p className="text-blue-100 text-sm">
                {course?.name} - {date} - {sessionType}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <FaTimes className="text-xl" />
            </button>
          </div>

          {/* Content */}
          <div className="flex flex-col lg:flex-row h-full max-h-[calc(90vh-80px)]">
            {/* Left Panel - Session Details */}
            <div className="lg:w-1/3 p-6 border-r border-gray-200 bg-gray-50">
              <h3 className="text-lg font-semibold mb-4">Session Details</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Location
                  </label>
                  <input
                    type="text"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={duration}
                    onChange={(e) => setDuration(parseInt(e.target.value))}
                    min="15"
                    max="300"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows="3"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Additional notes..."
                  />
                </div>

                {/* Quick Actions */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quick Mark All As:
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    {statusOptions.map(option => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => handleSelectAll(option.value)}
                        className={`px-3 py-2 rounded-md text-xs font-medium transition-colors ${option.color} hover:opacity-80`}
                      >
                        <option.icon className="inline mr-1" />
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Summary */}
                <div className="bg-white p-4 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center">
                    <FaUsers className="mr-2 text-blue-600" />
                    Summary
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-green-600">Present:</span>
                      <span className="font-medium">{statusCounts.Present}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-red-600">Absent:</span>
                      <span className="font-medium">{statusCounts.Absent}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-yellow-600">Late:</span>
                      <span className="font-medium">{statusCounts.Late}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-600">Excused:</span>
                      <span className="font-medium">{statusCounts.Excused}</span>
                    </div>
                    <div className="border-t pt-1 flex justify-between font-semibold">
                      <span>Total:</span>
                      <span>{studentList.length}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Panel - Student List */}
            <div className="lg:w-2/3 flex flex-col">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold">Students ({studentList.length})</h3>
              </div>

              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-4">
                  {studentList.map(student => (
                    <div key={student.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                          <div>
                            <h4 className="font-medium text-gray-900">{student.name}</h4>
                            <p className="text-sm text-gray-500">{student.rollNumber}</p>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-4 gap-2 mb-3">
                        {statusOptions.map(option => (
                          <button
                            key={option.value}
                            type="button"
                            onClick={() => handleStatusChange(student.id, option.value)}
                            className={`px-3 py-2 rounded-md text-xs font-medium transition-colors ${
                              attendanceData[student.id]?.status === option.value
                                ? option.color
                                : 'text-gray-600 bg-gray-200 hover:bg-gray-300'
                            }`}
                          >
                            <option.icon className="inline mr-1" />
                            {option.label}
                          </button>
                        ))}
                      </div>

                      <input
                        type="text"
                        value={attendanceData[student.id]?.notes || ''}
                        onChange={(e) => handleNotesChange(student.id, e.target.value)}
                        placeholder="Notes for this student..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Footer */}
              <div className="p-6 border-t border-gray-200 bg-gray-50">
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="spinner-small mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <FaSave className="mr-2" />
                        Save Attendance
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default AttendanceMarkingModal;
