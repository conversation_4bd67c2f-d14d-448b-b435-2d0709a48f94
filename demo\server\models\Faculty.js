import mongoose from 'mongoose';

const facultySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Faculty name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  designation: {
    type: String,
    required: [true, 'Designation is required'],
    enum: ['Professor', 'Associate Professor', 'Assistant Professor', 'Lecturer', 'Head of Department']
  },
  qualification: {
    type: [String],
    required: [true, 'Qualification is required']
  },
  specialization: {
    type: [String],
    required: [true, 'Area of expertise is required']
  },
  experience: {
    type: Number,
    required: [true, 'Experience is required'],
    min: [0, 'Experience cannot be negative']
  },
  profilePhoto: {
    type: String,
    default: '/images/default-avatar.jpg'
  },
  bio: {
    type: String,
    maxlength: [1000, 'Bio cannot exceed 1000 characters']
  },
  researchInterests: [String],
  publications: [{
    title: String,
    journal: String,
    year: Number,
    url: String
  }],
  awards: [{
    title: String,
    year: Number,
    description: String
  }],
  contact: {
    phone: String,
    office: String,
    officeHours: String
  },
  socialLinks: {
    linkedin: String,
    googleScholar: String,
    researchGate: String,
    website: String
  },
  courses: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  joinDate: {
    type: Date,
    default: Date.now
  },
  displayOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Index for search functionality
facultySchema.index({ 
  name: 'text', 
  specialization: 'text', 
  bio: 'text',
  'researchInterests': 'text'
});

// Virtual for full profile URL
facultySchema.virtual('profileUrl').get(function() {
  return `/faculty/${this._id}`;
});

export default mongoose.model('Faculty', facultySchema);
