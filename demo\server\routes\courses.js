import express from 'express';
import { body, validationResult } from 'express-validator';
import Course from '../models/Course.js';
import { protect, authorize, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// @desc    Get all courses
// @route   GET /api/courses
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      program, 
      semester,
      type,
      search,
      sortBy = 'semester'
    } = req.query;
    
    // Build query
    let query = { isActive: true };
    
    if (program) {
      query.program = program;
    }
    
    if (semester) {
      query.semester = parseInt(semester);
    }
    
    if (type) {
      query.type = type;
    }
    
    if (search) {
      query.$text = { $search: search };
    }
    
    // Build sort object
    let sort = {};
    switch (sortBy) {
      case 'name':
        sort = { name: 1 };
        break;
      case 'code':
        sort = { code: 1 };
        break;
      case 'credits':
        sort = { credits: -1 };
        break;
      default:
        sort = { semester: 1, name: 1 };
    }
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort,
      populate: {
        path: 'faculty prerequisites',
        select: 'name designation code'
      }
    };
    
    const courses = await Course.find(query)
      .populate(options.populate)
      .sort(options.sort)
      .limit(options.limit * 1)
      .skip((options.page - 1) * options.limit);
    
    const total = await Course.countDocuments(query);
    
    res.json({
      success: true,
      data: courses,
      pagination: {
        current: options.page,
        pages: Math.ceil(total / options.limit),
        total,
        limit: options.limit
      }
    });
  } catch (error) {
    console.error('Get courses error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching courses'
    });
  }
});

// @desc    Get courses by program and semester
// @route   GET /api/courses/program/:program/semester/:semester
// @access  Public
router.get('/program/:program/semester/:semester', async (req, res) => {
  try {
    const { program, semester } = req.params;
    
    const courses = await Course.find({
      program: program,
      semester: parseInt(semester),
      isActive: true
    })
    .populate('faculty', 'name designation')
    .populate('prerequisites', 'name code')
    .sort({ type: 1, name: 1 });
    
    res.json({
      success: true,
      data: courses
    });
  } catch (error) {
    console.error('Get courses by program/semester error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching courses'
    });
  }
});

// @desc    Get single course
// @route   GET /api/courses/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const course = await Course.findById(req.params.id)
      .populate('faculty', 'name designation email profilePhoto')
      .populate('prerequisites', 'name code description');
    
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }
    
    if (!course.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Course not available'
      });
    }
    
    res.json({
      success: true,
      data: course
    });
  } catch (error) {
    console.error('Get course by ID error:', error);
    
    if (error.name === 'CastError') {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while fetching course'
    });
  }
});

// @desc    Create new course
// @route   POST /api/courses
// @access  Private (Admin only)
router.post('/', [
  protect,
  authorize('admin'),
  body('code').trim().isLength({ min: 2, max: 20 }).withMessage('Course code must be between 2 and 20 characters'),
  body('name').trim().isLength({ min: 5, max: 200 }).withMessage('Course name must be between 5 and 200 characters'),
  body('description').trim().isLength({ min: 10, max: 1000 }).withMessage('Description must be between 10 and 1000 characters'),
  body('credits').isInt({ min: 1, max: 10 }).withMessage('Credits must be between 1 and 10'),
  body('semester').isInt({ min: 1, max: 8 }).withMessage('Semester must be between 1 and 8'),
  body('program').isIn(['B.Tech', 'M.Tech', 'Ph.D', 'MCA', 'BCA']).withMessage('Invalid program'),
  body('type').isIn(['Core', 'Elective', 'Lab', 'Project', 'Seminar']).withMessage('Invalid course type'),
  body('academicYear').notEmpty().withMessage('Academic year is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    // Check if course with code already exists
    const existingCourse = await Course.findOne({ code: req.body.code.toUpperCase() });
    if (existingCourse) {
      return res.status(400).json({
        success: false,
        message: 'Course with this code already exists'
      });
    }
    
    // Convert code to uppercase
    req.body.code = req.body.code.toUpperCase();
    
    const course = await Course.create(req.body);
    
    res.status(201).json({
      success: true,
      message: 'Course created successfully',
      data: course
    });
  } catch (error) {
    console.error('Create course error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating course'
    });
  }
});

// @desc    Update course
// @route   PUT /api/courses/:id
// @access  Private (Admin only)
router.put('/:id', [
  protect,
  authorize('admin'),
  body('code').optional().trim().isLength({ min: 2, max: 20 }).withMessage('Course code must be between 2 and 20 characters'),
  body('name').optional().trim().isLength({ min: 5, max: 200 }).withMessage('Course name must be between 5 and 200 characters'),
  body('description').optional().trim().isLength({ min: 10, max: 1000 }).withMessage('Description must be between 10 and 1000 characters'),
  body('credits').optional().isInt({ min: 1, max: 10 }).withMessage('Credits must be between 1 and 10'),
  body('semester').optional().isInt({ min: 1, max: 8 }).withMessage('Semester must be between 1 and 8'),
  body('program').optional().isIn(['B.Tech', 'M.Tech', 'Ph.D', 'MCA', 'BCA']).withMessage('Invalid program'),
  body('type').optional().isIn(['Core', 'Elective', 'Lab', 'Project', 'Seminar']).withMessage('Invalid course type')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    // Convert code to uppercase if provided
    if (req.body.code) {
      req.body.code = req.body.code.toUpperCase();
    }
    
    const course = await Course.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('faculty', 'name designation')
     .populate('prerequisites', 'name code');
    
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Course updated successfully',
      data: course
    });
  } catch (error) {
    console.error('Update course error:', error);
    
    if (error.name === 'CastError') {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while updating course'
    });
  }
});

// @desc    Delete course
// @route   DELETE /api/courses/:id
// @access  Private (Admin only)
router.delete('/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const course = await Course.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );
    
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Course deactivated successfully'
    });
  } catch (error) {
    console.error('Delete course error:', error);
    
    if (error.name === 'CastError') {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while deleting course'
    });
  }
});

export default router;
