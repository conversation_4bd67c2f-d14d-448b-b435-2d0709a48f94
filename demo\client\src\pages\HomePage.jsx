import { useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation, EffectFade } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import 'swiper/css/effect-fade';
import { FaArrowRight, FaGraduationCap, FaLaptopCode, FaUsers } from 'react-icons/fa';

// Components
import NewsTickerComponent from '../components/ui/NewsTicker';
import FeaturedEvents from '../components/sections/FeaturedEvents';
import AchievementCounter from '../components/ui/AchievementCounter';
import ChatbotButton from '../components/ui/ChatbotButton';

const HomePage = () => {
  const swiperRef = useRef(null);

  const heroSlides = [
    {
      image: '/images/hero1.jpg',
      title: 'B.Sc. Computer Science with Cognitive Systems',
      subtitle: 'Industry Integrated Degree Program in Collaboration with TCS',
      description: 'We bring the real-world to the classroom through TCS\' Academic Interface Program (AIP)',
    },
    {
      image: '/images/hero2.jpg',
      title: 'Cutting-Edge Research',
      subtitle: 'Pushing the boundaries of technology',
      description: 'Building a talent pool of industry-ready graduates',
    },
    {
      image: '/images/hero3.jpg',
      title: 'Industry Connections',
      subtitle: 'Bridging academia and real-world applications',
      description: 'A full-fledged course curriculum in the field of IT',
    },
  ];

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <>
      {/* Hero Section with Carousel */}
      <section className="relative h-screen">
        <Swiper
          ref={swiperRef}
          modules={[Autoplay, Pagination, Navigation, EffectFade]}
          effect="fade"
          slidesPerView={1}
          loop={true}
          autoplay={{ delay: 5000, disableOnInteraction: false }}
          pagination={{ clickable: true }}
          navigation={true}
          className="h-full"
        >
          {heroSlides.map((slide, index) => (
            <SwiperSlide key={index}>
              <div 
                className="h-full bg-cover bg-center relative"
                style={{ backgroundImage: `url(${slide.image})` }}
              >
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="text-center text-white px-4 max-w-6xl">
                    {index === 0 && (
                      <motion.div
                        className="mb-6"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.8 }}
                      >
                        <img
                          src="/images/department-logo.jpg"
                          alt="B.Sc. Computer Science with Cognitive Systems"
                          className="h-24 md:h-32 w-auto mx-auto rounded-lg shadow-2xl mb-4"
                        />
                      </motion.div>
                    )}
                    <motion.h1
                      className="text-3xl md:text-5xl font-bold mb-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                    >
                      {index === 0 ? (
                        <>
                          <span className="text-yellow-400">B.Sc. Computer Science</span>
                          <br />
                          <span className="text-2xl md:text-3xl text-blue-200">with Cognitive Systems</span>
                        </>
                      ) : (
                        slide.title
                      )}
                    </motion.h1>
                    <motion.p
                      className="text-lg md:text-xl mb-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                    >
                      {index === 0 ? (
                        <>
                          <span className="text-white">Industry Integrated Degree Program</span>
                          <br />
                          <span className="text-yellow-300 font-semibold">In Collaboration with TATA CONSULTANCY SERVICES (TCS)</span>
                        </>
                      ) : (
                        slide.subtitle
                      )}
                    </motion.p>
                    <motion.p
                      className="text-base md:text-lg mb-8 text-blue-100"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.6 }}
                    >
                      {slide.description}
                    </motion.p>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.6 }}
                    >
                      <Link 
                        to="/about" 
                        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md inline-flex items-center transition-colors mr-4"
                      >
                        Explore Programs <FaArrowRight className="ml-2" />
                      </Link>
                      <Link 
                        to="/faculty" 
                        className="bg-transparent hover:bg-white hover:text-blue-900 text-white border-2 border-white px-6 py-3 rounded-md inline-flex items-center transition-colors"
                      >
                        Meet the Team <FaUsers className="ml-2" />
                      </Link>
                    </motion.div>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </section>

      {/* News Ticker */}
      <NewsTickerComponent />

      {/* Quick Stats */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true, amount: 0.3 }}
          >
            <AchievementCounter 
              icon={<FaGraduationCap className="text-5xl text-blue-600" />}
              count={5000}
              title="Alumni"
              description="Successful graduates worldwide"
            />
            <AchievementCounter 
              icon={<FaLaptopCode className="text-5xl text-blue-600" />}
              count={50}
              title="Research Projects"
              description="Ongoing innovative research"
            />
            <AchievementCounter 
              icon={<FaUsers className="text-5xl text-blue-600" />}
              count={30}
              title="Faculty Members"
              description="Experienced professors and researchers"
            />
          </motion.div>
        </div>
      </section>

      {/* Featured Programs */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.div 
            className="text-center mb-12"
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-2">Our Programs</h2>
            <p className="text-gray-600">Comprehensive education in computer science and technology</p>
          </motion.div>
          
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true, amount: 0.3 }}
          >
            {[
              {
                title: "B.Sc. Computer Science with Cognitive Systems",
                description: "Industry-integrated three-year undergraduate program in collaboration with TCS, focusing on cognitive systems and real-world applications",
                icon: "🎓",
                link: "/courses",
                highlight: true
              },
              {
                title: "TCS Academic Interface Program (AIP)",
                description: "Industry-relevant course curriculum designed and deployed in collaboration with TCS experts",
                icon: "🏢",
                link: "/courses"
              },
              {
                title: "Research & Innovation",
                description: "Cutting-edge research opportunities in cognitive systems, AI, and emerging technologies",
                icon: "🔬",
                link: "/courses"
              }
            ].map((program, index) => (
              <motion.div
                key={index}
                className={`p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow ${
                  program.highlight
                    ? 'bg-gradient-to-br from-blue-50 to-yellow-50 border-2 border-yellow-400'
                    : 'bg-white'
                }`}
                variants={fadeInUp}
              >
                <div className="text-4xl mb-4">{program.icon}</div>
                <h3 className={`text-xl font-bold mb-2 ${
                  program.highlight ? 'text-blue-800' : 'text-gray-800'
                }`}>
                  {program.title}
                </h3>
                <p className="text-gray-600 mb-4">{program.description}</p>
                <Link
                  to={program.link}
                  className={`inline-flex items-center ${
                    program.highlight
                      ? 'text-blue-700 hover:text-blue-900 font-semibold'
                      : 'text-blue-600 hover:text-blue-800'
                  }`}
                >
                  Learn more <FaArrowRight className="ml-2" />
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Featured Events */}
      <FeaturedEvents />

      {/* Chatbot Button */}
      <ChatbotButton />
    </>
  );
};

export default HomePage;