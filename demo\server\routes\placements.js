import express from 'express';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Sample placement data - in real app, this would be from database
const placementData = {
  statistics: {
    "2024": {
      totalStudents: 120,
      placedStudents: 115,
      placementPercentage: 95.8,
      averagePackage: 8.5,
      highestPackage: 45.0,
      medianPackage: 7.2
    },
    "2023": {
      totalStudents: 110,
      placedStudents: 105,
      placementPercentage: 95.5,
      averagePackage: 7.8,
      highestPackage: 42.0,
      medianPackage: 6.8
    },
    "2022": {
      totalStudents: 100,
      placedStudents: 94,
      placementPercentage: 94.0,
      averagePackage: 7.2,
      highestPackage: 38.0,
      medianPackage: 6.5
    }
  },
  companies: [
    {
      id: 1,
      name: "Google",
      logo: "/images/companies/google.png",
      type: "Product",
      sector: "Technology",
      studentsPlaced: 8,
      averagePackage: 35.0,
      roles: ["Software Engineer", "Data Scientist", "Product Manager"],
      visitYear: 2024
    },
    {
      id: 2,
      name: "Microsoft",
      logo: "/images/companies/microsoft.png",
      type: "Product",
      sector: "Technology",
      studentsPlaced: 12,
      averagePackage: 28.0,
      roles: ["Software Engineer", "Cloud Engineer", "AI Engineer"],
      visitYear: 2024
    },
    {
      id: 3,
      name: "Amazon",
      logo: "/images/companies/amazon.png",
      type: "Product",
      sector: "E-commerce/Cloud",
      studentsPlaced: 15,
      averagePackage: 22.0,
      roles: ["Software Engineer", "DevOps Engineer", "Data Engineer"],
      visitYear: 2024
    },
    {
      id: 4,
      name: "Meta",
      logo: "/images/companies/meta.png",
      type: "Product",
      sector: "Social Media",
      studentsPlaced: 6,
      averagePackage: 32.0,
      roles: ["Software Engineer", "ML Engineer", "Frontend Engineer"],
      visitYear: 2024
    },
    {
      id: 5,
      name: "Apple",
      logo: "/images/companies/apple.png",
      type: "Product",
      sector: "Technology",
      studentsPlaced: 4,
      averagePackage: 38.0,
      roles: ["iOS Developer", "Software Engineer", "Hardware Engineer"],
      visitYear: 2024
    }
  ],
  alumni: [
    {
      id: 1,
      name: "Rahul Sharma",
      batch: "2020",
      currentPosition: "Senior Software Engineer",
      company: "Google",
      location: "Bangalore",
      photo: "/images/alumni/rahul.jpg",
      testimonial: "The CS department provided me with a strong foundation in computer science. The faculty's guidance and the practical exposure through projects helped me land my dream job at Google.",
      linkedIn: "https://linkedin.com/in/rahulsharma",
      achievements: ["Google Code Jam Finalist", "Published 3 research papers"]
    },
    {
      id: 2,
      name: "Priya Patel",
      batch: "2019",
      currentPosition: "Principal Data Scientist",
      company: "Microsoft",
      location: "Seattle, USA",
      photo: "/images/alumni/priya.jpg",
      testimonial: "The research opportunities and mentorship I received during my time in the department were invaluable. It shaped my career in data science and AI.",
      linkedIn: "https://linkedin.com/in/priyapatel",
      achievements: ["Microsoft AI MVP", "Speaker at major conferences"]
    },
    {
      id: 3,
      name: "Arjun Kumar",
      batch: "2021",
      currentPosition: "Product Manager",
      company: "Amazon",
      location: "Hyderabad",
      photo: "/images/alumni/arjun.jpg",
      testimonial: "The department's emphasis on both technical skills and soft skills prepared me well for a product management role. The industry connections were also very helpful.",
      linkedIn: "https://linkedin.com/in/arjunkumar",
      achievements: ["Led product launches", "Startup founder"]
    }
  ]
};

// @desc    Get placement statistics
// @route   GET /api/placements/statistics
// @access  Public
router.get('/statistics', async (req, res) => {
  try {
    const { year } = req.query;
    
    if (year) {
      const yearStats = placementData.statistics[year];
      if (!yearStats) {
        return res.status(404).json({
          success: false,
          message: 'Statistics not found for the specified year'
        });
      }
      
      res.json({
        success: true,
        data: {
          year: parseInt(year),
          ...yearStats
        }
      });
    } else {
      // Return all years
      const allStats = Object.entries(placementData.statistics).map(([year, stats]) => ({
        year: parseInt(year),
        ...stats
      })).sort((a, b) => b.year - a.year);
      
      res.json({
        success: true,
        data: allStats
      });
    }
  } catch (error) {
    console.error('Get placement statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching placement statistics'
    });
  }
});

// @desc    Get recruiting companies
// @route   GET /api/placements/companies
// @access  Public
router.get('/companies', async (req, res) => {
  try {
    const { sector, type, year, limit } = req.query;
    
    let companies = [...placementData.companies];
    
    // Filter by sector
    if (sector) {
      companies = companies.filter(company => 
        company.sector.toLowerCase().includes(sector.toLowerCase())
      );
    }
    
    // Filter by type
    if (type) {
      companies = companies.filter(company => 
        company.type.toLowerCase() === type.toLowerCase()
      );
    }
    
    // Filter by visit year
    if (year) {
      companies = companies.filter(company => 
        company.visitYear === parseInt(year)
      );
    }
    
    // Sort by average package (highest first)
    companies.sort((a, b) => b.averagePackage - a.averagePackage);
    
    // Limit results
    if (limit) {
      companies = companies.slice(0, parseInt(limit));
    }
    
    res.json({
      success: true,
      data: companies,
      total: companies.length
    });
  } catch (error) {
    console.error('Get companies error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching companies'
    });
  }
});

// @desc    Get alumni testimonials
// @route   GET /api/placements/alumni
// @access  Public
router.get('/alumni', async (req, res) => {
  try {
    const { batch, company, limit = 10 } = req.query;
    
    let alumni = [...placementData.alumni];
    
    // Filter by batch
    if (batch) {
      alumni = alumni.filter(alum => alum.batch === batch);
    }
    
    // Filter by company
    if (company) {
      alumni = alumni.filter(alum => 
        alum.company.toLowerCase().includes(company.toLowerCase())
      );
    }
    
    // Sort by batch (newest first)
    alumni.sort((a, b) => parseInt(b.batch) - parseInt(a.batch));
    
    // Limit results
    alumni = alumni.slice(0, parseInt(limit));
    
    res.json({
      success: true,
      data: alumni,
      total: alumni.length
    });
  } catch (error) {
    console.error('Get alumni error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching alumni data'
    });
  }
});

// @desc    Get placement overview
// @route   GET /api/placements/overview
// @access  Public
router.get('/overview', async (req, res) => {
  try {
    const currentYear = new Date().getFullYear();
    const currentStats = placementData.statistics[currentYear] || placementData.statistics["2024"];
    
    // Get top companies by students placed
    const topCompanies = placementData.companies
      .sort((a, b) => b.studentsPlaced - a.studentsPlaced)
      .slice(0, 5);
    
    // Get recent alumni
    const recentAlumni = placementData.alumni
      .sort((a, b) => parseInt(b.batch) - parseInt(a.batch))
      .slice(0, 3);
    
    // Calculate sector-wise distribution
    const sectorDistribution = placementData.companies.reduce((acc, company) => {
      acc[company.sector] = (acc[company.sector] || 0) + company.studentsPlaced;
      return acc;
    }, {});
    
    res.json({
      success: true,
      data: {
        currentYearStats: {
          year: currentYear,
          ...currentStats
        },
        topCompanies,
        recentAlumni,
        sectorDistribution,
        totalCompaniesVisited: placementData.companies.length,
        totalAlumniNetwork: placementData.alumni.length * 50 // Estimated
      }
    });
  } catch (error) {
    console.error('Get placement overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching placement overview'
    });
  }
});

export default router;
