import express from 'express';
import { body, validationResult } from 'express-validator';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Sample announcements data - in real app, this would be from database
let announcements = [
  {
    id: 1,
    title: "New AI & Machine Learning Lab Inaugurated",
    content: "We are excited to announce the inauguration of our state-of-the-art AI & Machine Learning Lab equipped with the latest hardware and software for advanced research and learning.",
    type: "announcement",
    priority: "high",
    isActive: true,
    startDate: "2024-01-15",
    endDate: "2024-02-15",
    targetAudience: ["students", "faculty"],
    createdBy: "admin",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z"
  },
  {
    id: 2,
    title: "Spring 2024 Semester Registration Open",
    content: "Registration for Spring 2024 semester is now open. Students are advised to complete their course registration before the deadline. Last date for registration: January 30th, 2024.",
    type: "important",
    priority: "high",
    isActive: true,
    startDate: "2024-01-10",
    endDate: "2024-01-30",
    targetAudience: ["students"],
    createdBy: "admin",
    createdAt: "2024-01-10T09:00:00Z",
    updatedAt: "2024-01-10T09:00:00Z"
  },
  {
    id: 3,
    title: "National Hackathon 2024 - Registration Open",
    content: "Participate in the National Hackathon 2024! Show your coding skills and win exciting prizes. Registration deadline: February 15th, 2024. Event dates: February 20-22, 2024.",
    type: "event",
    priority: "medium",
    isActive: true,
    startDate: "2024-01-08",
    endDate: "2024-02-15",
    targetAudience: ["students"],
    createdBy: "admin",
    createdAt: "2024-01-08T14:00:00Z",
    updatedAt: "2024-01-08T14:00:00Z"
  },
  {
    id: 4,
    title: "Guest Lecture: Future of Cloud Computing",
    content: "Join us for an insightful guest lecture by Mr. Raj Patel, Cloud Solutions Architect at Amazon Web Services, on 'The Future of Cloud Computing'. Date: January 25th, 2024, Time: 2:00 PM - 4:00 PM, Venue: Seminar Hall 1.",
    type: "event",
    priority: "medium",
    isActive: true,
    startDate: "2024-01-05",
    endDate: "2024-01-25",
    targetAudience: ["students", "faculty"],
    createdBy: "admin",
    createdAt: "2024-01-05T11:00:00Z",
    updatedAt: "2024-01-05T11:00:00Z"
  },
  {
    id: 5,
    title: "Placement Drive - Microsoft, Amazon, Google",
    content: "Major placement drive starting February 1st, 2024. Companies visiting: Microsoft, Amazon, Google, and more. Eligible students should prepare their resumes and register through the placement portal.",
    type: "placement",
    priority: "high",
    isActive: true,
    startDate: "2024-01-03",
    endDate: "2024-03-01",
    targetAudience: ["students"],
    createdBy: "admin",
    createdAt: "2024-01-03T16:00:00Z",
    updatedAt: "2024-01-03T16:00:00Z"
  }
];

// @desc    Get all announcements
// @route   GET /api/announcements
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { 
      type, 
      priority, 
      audience, 
      active = 'true',
      limit = 10,
      page = 1
    } = req.query;
    
    let filteredAnnouncements = [...announcements];
    
    // Filter by active status
    if (active === 'true') {
      const currentDate = new Date().toISOString().split('T')[0];
      filteredAnnouncements = filteredAnnouncements.filter(announcement => 
        announcement.isActive && 
        announcement.startDate <= currentDate &&
        announcement.endDate >= currentDate
      );
    }
    
    // Filter by type
    if (type) {
      filteredAnnouncements = filteredAnnouncements.filter(announcement => 
        announcement.type === type
      );
    }
    
    // Filter by priority
    if (priority) {
      filteredAnnouncements = filteredAnnouncements.filter(announcement => 
        announcement.priority === priority
      );
    }
    
    // Filter by target audience
    if (audience) {
      filteredAnnouncements = filteredAnnouncements.filter(announcement => 
        announcement.targetAudience.includes(audience)
      );
    }
    
    // Sort by priority and creation date
    filteredAnnouncements.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return new Date(b.createdAt) - new Date(a.createdAt);
    });
    
    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedAnnouncements = filteredAnnouncements.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: paginatedAnnouncements,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(filteredAnnouncements.length / parseInt(limit)),
        total: filteredAnnouncements.length,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get announcements error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching announcements'
    });
  }
});

// @desc    Get single announcement
// @route   GET /api/announcements/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const announcementId = parseInt(req.params.id);
    const announcement = announcements.find(ann => ann.id === announcementId);
    
    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: 'Announcement not found'
      });
    }
    
    res.json({
      success: true,
      data: announcement
    });
  } catch (error) {
    console.error('Get announcement error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching announcement'
    });
  }
});

// @desc    Get announcements for news ticker
// @route   GET /api/announcements/ticker/active
// @access  Public
router.get('/ticker/active', async (req, res) => {
  try {
    const currentDate = new Date().toISOString().split('T')[0];
    
    const tickerAnnouncements = announcements
      .filter(announcement => 
        announcement.isActive && 
        announcement.startDate <= currentDate &&
        announcement.endDate >= currentDate
      )
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      })
      .slice(0, 5) // Limit to 5 most important announcements
      .map(announcement => ({
        id: announcement.id,
        title: announcement.title,
        type: announcement.type,
        priority: announcement.priority
      }));
    
    res.json({
      success: true,
      data: tickerAnnouncements
    });
  } catch (error) {
    console.error('Get ticker announcements error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching ticker announcements'
    });
  }
});

// @desc    Create new announcement
// @route   POST /api/announcements
// @access  Private (Admin/Faculty only)
router.post('/', [
  protect,
  authorize('admin', 'faculty'),
  body('title').trim().isLength({ min: 5, max: 200 }).withMessage('Title must be between 5 and 200 characters'),
  body('content').trim().isLength({ min: 10, max: 2000 }).withMessage('Content must be between 10 and 2000 characters'),
  body('type').isIn(['announcement', 'important', 'event', 'placement', 'academic']).withMessage('Invalid announcement type'),
  body('priority').isIn(['high', 'medium', 'low']).withMessage('Invalid priority'),
  body('startDate').isISO8601().withMessage('Invalid start date'),
  body('endDate').isISO8601().withMessage('Invalid end date'),
  body('targetAudience').isArray({ min: 1 }).withMessage('At least one target audience is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    // Validate date logic
    const startDate = new Date(req.body.startDate);
    const endDate = new Date(req.body.endDate);
    
    if (endDate < startDate) {
      return res.status(400).json({
        success: false,
        message: 'End date cannot be before start date'
      });
    }
    
    // Create new announcement
    const newAnnouncement = {
      id: announcements.length + 1,
      ...req.body,
      isActive: true,
      createdBy: req.user.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    announcements.push(newAnnouncement);
    
    res.status(201).json({
      success: true,
      message: 'Announcement created successfully',
      data: newAnnouncement
    });
  } catch (error) {
    console.error('Create announcement error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating announcement'
    });
  }
});

// @desc    Update announcement
// @route   PUT /api/announcements/:id
// @access  Private (Admin/Faculty only)
router.put('/:id', [
  protect,
  authorize('admin', 'faculty'),
  body('title').optional().trim().isLength({ min: 5, max: 200 }).withMessage('Title must be between 5 and 200 characters'),
  body('content').optional().trim().isLength({ min: 10, max: 2000 }).withMessage('Content must be between 10 and 2000 characters'),
  body('type').optional().isIn(['announcement', 'important', 'event', 'placement', 'academic']).withMessage('Invalid announcement type'),
  body('priority').optional().isIn(['high', 'medium', 'low']).withMessage('Invalid priority')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const announcementId = parseInt(req.params.id);
    const announcementIndex = announcements.findIndex(ann => ann.id === announcementId);
    
    if (announcementIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Announcement not found'
      });
    }
    
    // Update announcement
    announcements[announcementIndex] = {
      ...announcements[announcementIndex],
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    res.json({
      success: true,
      message: 'Announcement updated successfully',
      data: announcements[announcementIndex]
    });
  } catch (error) {
    console.error('Update announcement error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating announcement'
    });
  }
});

// @desc    Delete announcement
// @route   DELETE /api/announcements/:id
// @access  Private (Admin only)
router.delete('/:id', protect, authorize('admin'), async (req, res) => {
  try {
    const announcementId = parseInt(req.params.id);
    const announcementIndex = announcements.findIndex(ann => ann.id === announcementId);
    
    if (announcementIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Announcement not found'
      });
    }
    
    // Soft delete - set isActive to false
    announcements[announcementIndex].isActive = false;
    announcements[announcementIndex].updatedAt = new Date().toISOString();
    
    res.json({
      success: true,
      message: 'Announcement deleted successfully'
    });
  } catch (error) {
    console.error('Delete announcement error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting announcement'
    });
  }
});

export default router;
