import mongoose from 'mongoose';

const eventSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Event title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Event description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  type: {
    type: String,
    required: [true, 'Event type is required'],
    enum: ['Seminar', 'Workshop', 'Conference', 'Hackathon', 'Guest Lecture', 'Cultural', 'Sports', 'Other']
  },
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required']
  },
  startTime: {
    type: String,
    required: [true, 'Start time is required']
  },
  endTime: {
    type: String,
    required: [true, 'End time is required']
  },
  venue: {
    type: String,
    required: [true, 'Venue is required'],
    maxlength: [200, 'Venue cannot exceed 200 characters']
  },
  organizer: {
    type: String,
    required: [true, 'Organizer is required'],
    maxlength: [200, 'Organizer cannot exceed 200 characters']
  },
  speaker: {
    name: String,
    designation: String,
    organization: String,
    bio: String,
    photo: String
  },
  registrationRequired: {
    type: Boolean,
    default: false
  },
  registrationLink: {
    type: String,
    default: null
  },
  maxParticipants: {
    type: Number,
    default: null
  },
  currentParticipants: {
    type: Number,
    default: 0
  },
  images: [{
    url: String,
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  documents: [{
    name: String,
    url: String,
    type: String // 'brochure', 'schedule', 'certificate', etc.
  }],
  tags: [String],
  status: {
    type: String,
    enum: ['upcoming', 'ongoing', 'completed', 'cancelled'],
    default: 'upcoming'
  },
  isPublished: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Index for search functionality
eventSchema.index({ 
  title: 'text', 
  description: 'text', 
  tags: 'text',
  'speaker.name': 'text'
});

// Virtual for event status based on dates
eventSchema.virtual('currentStatus').get(function() {
  const now = new Date();
  const start = new Date(this.startDate);
  const end = new Date(this.endDate);
  
  if (now < start) return 'upcoming';
  if (now >= start && now <= end) return 'ongoing';
  return 'completed';
});

// Method to check if registration is open
eventSchema.methods.isRegistrationOpen = function() {
  if (!this.registrationRequired) return false;
  if (this.maxParticipants && this.currentParticipants >= this.maxParticipants) return false;
  return new Date() < new Date(this.startDate);
};

export default mongoose.model('Event', eventSchema);
