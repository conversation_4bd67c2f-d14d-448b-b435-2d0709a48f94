import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaCalendarAlt, 
  FaChartPie, 
  FaUserCheck, 
  FaUserTimes, 
  FaClock, 
  FaExclamationTriangle,
  FaBook,
  FaPercentage,
  FaFilter,
  FaDownload
} from 'react-icons/fa';

const StudentAttendanceView = ({ studentId }) => {
  const [attendanceData, setAttendanceData] = useState([]);
  const [courseStats, setCourseStats] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState('all');
  const [selectedMonth, setSelectedMonth] = useState('all');
  const [loading, setLoading] = useState(true);

  // Sample data for demonstration
  const sampleAttendanceData = [
    {
      id: 1,
      course: { name: 'Data Structures and Algorithms', code: 'CS301' },
      date: '2024-01-20',
      status: 'Present',
      sessionType: 'Lecture',
      timeIn: '09:15 AM',
      location: 'Lecture Hall 1'
    },
    {
      id: 2,
      course: { name: 'Database Management Systems', code: 'CS302' },
      date: '2024-01-20',
      status: 'Present',
      sessionType: 'Lab',
      timeIn: '02:30 PM',
      location: 'Database Lab'
    },
    {
      id: 3,
      course: { name: 'Computer Networks', code: 'CS303' },
      date: '2024-01-19',
      status: 'Late',
      sessionType: 'Lecture',
      timeIn: '09:25 AM',
      location: 'Lecture Hall 2'
    },
    {
      id: 4,
      course: { name: 'Operating Systems', code: 'CS304' },
      date: '2024-01-19',
      status: 'Absent',
      sessionType: 'Tutorial',
      timeIn: '-',
      location: 'Tutorial Room'
    }
  ];

  const sampleCourseStats = [
    {
      course: { name: 'Data Structures and Algorithms', code: 'CS301' },
      total: 25,
      present: 22,
      absent: 2,
      late: 1,
      excused: 0,
      attendancePercentage: 92
    },
    {
      course: { name: 'Database Management Systems', code: 'CS302' },
      total: 20,
      present: 18,
      absent: 1,
      late: 1,
      excused: 0,
      attendancePercentage: 95
    },
    {
      course: { name: 'Computer Networks', code: 'CS303' },
      total: 22,
      present: 19,
      absent: 2,
      late: 1,
      excused: 0,
      attendancePercentage: 91
    },
    {
      course: { name: 'Operating Systems', code: 'CS304' },
      total: 18,
      present: 15,
      absent: 3,
      late: 0,
      excused: 0,
      attendancePercentage: 83
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setAttendanceData(sampleAttendanceData);
      setCourseStats(sampleCourseStats);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Present': return 'text-green-600 bg-green-100';
      case 'Absent': return 'text-red-600 bg-red-100';
      case 'Late': return 'text-yellow-600 bg-yellow-100';
      case 'Excused': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Present': return FaUserCheck;
      case 'Absent': return FaUserTimes;
      case 'Late': return FaClock;
      case 'Excused': return FaExclamationTriangle;
      default: return FaUserCheck;
    }
  };

  const getAttendanceColor = (percentage) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredAttendance = attendanceData.filter(record => {
    const courseMatch = selectedCourse === 'all' || record.course.code === selectedCourse;
    const monthMatch = selectedMonth === 'all' || new Date(record.date).getMonth() === parseInt(selectedMonth);
    return courseMatch && monthMatch;
  });

  const overallAttendance = courseStats.length > 0 
    ? Math.round(courseStats.reduce((sum, stat) => sum + stat.attendancePercentage, 0) / courseStats.length)
    : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="spinner mx-auto mb-4"></div>
        <p className="text-gray-600">Loading attendance data...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg shadow-md p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Overall Attendance</p>
              <p className={`text-3xl font-bold ${getAttendanceColor(overallAttendance)}`}>
                {overallAttendance}%
              </p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <FaPercentage className="text-blue-600 text-xl" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-white rounded-lg shadow-md p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Total Classes</p>
              <p className="text-3xl font-bold text-gray-800">
                {courseStats.reduce((sum, stat) => sum + stat.total, 0)}
              </p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <FaBook className="text-purple-600 text-xl" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-lg shadow-md p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Classes Attended</p>
              <p className="text-3xl font-bold text-green-600">
                {courseStats.reduce((sum, stat) => sum + stat.present + stat.late, 0)}
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <FaUserCheck className="text-green-600 text-xl" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="bg-white rounded-lg shadow-md p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Classes Missed</p>
              <p className="text-3xl font-bold text-red-600">
                {courseStats.reduce((sum, stat) => sum + stat.absent, 0)}
              </p>
            </div>
            <div className="bg-red-100 p-3 rounded-full">
              <FaUserTimes className="text-red-600 text-xl" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Course-wise Attendance */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-white rounded-lg shadow-md p-6"
      >
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <FaChartPie className="mr-2 text-blue-600" />
          Course-wise Attendance
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {courseStats.map((stat, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h4 className="font-medium text-gray-800">{stat.course.name}</h4>
                  <p className="text-sm text-gray-500">{stat.course.code}</p>
                </div>
                <span className={`text-lg font-bold ${getAttendanceColor(stat.attendancePercentage)}`}>
                  {stat.attendancePercentage}%
                </span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Present:</span>
                  <span className="text-green-600 font-medium">{stat.present}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Absent:</span>
                  <span className="text-red-600 font-medium">{stat.absent}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Late:</span>
                  <span className="text-yellow-600 font-medium">{stat.late}</span>
                </div>
                <div className="flex justify-between text-sm border-t pt-2">
                  <span className="text-gray-600 font-medium">Total:</span>
                  <span className="text-gray-800 font-medium">{stat.total}</span>
                </div>
              </div>
              
              {/* Progress bar */}
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      stat.attendancePercentage >= 90 ? 'bg-green-500' :
                      stat.attendancePercentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${stat.attendancePercentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
        className="bg-white rounded-lg shadow-md p-6"
      >
        <div className="flex flex-col md:flex-row gap-4 items-end">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Course
            </label>
            <select
              value={selectedCourse}
              onChange={(e) => setSelectedCourse(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Courses</option>
              {courseStats.map(stat => (
                <option key={stat.course.code} value={stat.course.code}>
                  {stat.course.code} - {stat.course.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Month
            </label>
            <select
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Months</option>
              <option value="0">January</option>
              <option value="1">February</option>
              <option value="2">March</option>
              <option value="3">April</option>
              <option value="4">May</option>
              <option value="5">June</option>
              <option value="6">July</option>
              <option value="7">August</option>
              <option value="8">September</option>
              <option value="9">October</option>
              <option value="10">November</option>
              <option value="11">December</option>
            </select>
          </div>

          <button className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md transition-colors duration-200 flex items-center">
            <FaDownload className="mr-2" />
            Export
          </button>
        </div>
      </motion.div>

      {/* Attendance Records */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="bg-white rounded-lg shadow-md overflow-hidden"
      >
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center">
            <FaCalendarAlt className="mr-2 text-blue-600" />
            Recent Attendance Records ({filteredAttendance.length})
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Course
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Session Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time In
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAttendance.map((record) => {
                const StatusIcon = getStatusIcon(record.status);
                return (
                  <tr key={record.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(record.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{record.course.name}</div>
                      <div className="text-sm text-gray-500">{record.course.code}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {record.sessionType}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                        <StatusIcon className="mr-1" />
                        {record.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {record.timeIn}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.location}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </motion.div>
    </div>
  );
};

export default StudentAttendanceView;
