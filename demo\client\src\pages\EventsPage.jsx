import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaCalendarAlt, 
  FaMapMarkerAlt, 
  FaClock, 
  FaUsers,
  FaFilter,
  FaSearch,
  FaExternalLinkAlt,
  FaTag
} from 'react-icons/fa';

const EventsPage = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Sample events data
  const sampleEvents = [
    {
      id: 1,
      title: "AI & Machine Learning Workshop",
      description: "Hands-on workshop covering latest trends in AI and ML with industry experts from Google and Microsoft.",
      type: "Workshop",
      status: "upcoming",
      startDate: "2024-02-15",
      endDate: "2024-02-15",
      startTime: "10:00 AM",
      endTime: "4:00 PM",
      venue: "AI Lab, Block A",
      organizer: "CS Department",
      speaker: {
        name: "Dr. <PERSON>",
        designation: "Senior AI Researcher",
        organization: "Google AI"
      },
      registrationRequired: true,
      maxParticipants: 50,
      currentParticipants: 35,
      registrationLink: "#",
      image: "/images/events/ai-workshop.jpg",
      tags: ["AI", "Machine Learning", "Workshop"]
    },
    {
      id: 2,
      title: "National Hackathon 2024",
      description: "48-hour coding marathon with exciting prizes and opportunities to showcase your programming skills.",
      type: "Hackathon",
      status: "upcoming",
      startDate: "2024-02-20",
      endDate: "2024-02-22",
      startTime: "9:00 AM",
      endTime: "9:00 AM",
      venue: "Main Auditorium",
      organizer: "CS Department",
      registrationRequired: true,
      maxParticipants: 200,
      currentParticipants: 150,
      registrationLink: "#",
      image: "/images/events/hackathon.jpg",
      tags: ["Hackathon", "Programming", "Competition"],
      prizes: ["₹50,000", "₹30,000", "₹20,000"]
    },
    {
      id: 3,
      title: "Industry Connect: Future of Cloud Computing",
      description: "Guest lecture by industry leaders discussing the evolution and future prospects of cloud technologies.",
      type: "Guest Lecture",
      status: "upcoming",
      startDate: "2024-02-25",
      endDate: "2024-02-25",
      startTime: "2:00 PM",
      endTime: "4:00 PM",
      venue: "Seminar Hall 1",
      organizer: "CS Department",
      speaker: {
        name: "Mr. Raj Patel",
        designation: "Cloud Solutions Architect",
        organization: "Amazon Web Services"
      },
      registrationRequired: false,
      image: "/images/events/cloud-computing.jpg",
      tags: ["Cloud Computing", "Industry", "Guest Lecture"]
    },
    {
      id: 4,
      title: "Research Paper Presentation",
      description: "Students presenting their research work in various domains of computer science.",
      type: "Seminar",
      status: "completed",
      startDate: "2024-01-10",
      endDate: "2024-01-10",
      startTime: "10:00 AM",
      endTime: "5:00 PM",
      venue: "Conference Hall",
      organizer: "CS Department",
      registrationRequired: false,
      image: "/images/events/research-presentation.jpg",
      tags: ["Research", "Presentation", "Academic"]
    }
  ];

  const eventTypes = ["Workshop", "Hackathon", "Guest Lecture", "Seminar", "Conference", "Cultural"];
  const eventStatuses = ["upcoming", "ongoing", "completed"];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setEvents(sampleEvents);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredEvents = events.filter(event => {
    const matchesType = selectedType === 'all' || event.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || event.status === selectedStatus;
    const matchesSearch = !searchQuery || 
      event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesType && matchesStatus && matchesSearch;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'upcoming': return 'bg-green-100 text-green-800';
      case 'ongoing': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'Workshop': return 'bg-blue-100 text-blue-800';
      case 'Hackathon': return 'bg-purple-100 text-purple-800';
      case 'Guest Lecture': return 'bg-green-100 text-green-800';
      case 'Seminar': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading events...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Events & Workshops
            </h1>
            <p className="text-xl md:text-2xl text-purple-100 max-w-3xl mx-auto">
              Stay updated with our latest events, workshops, and academic activities
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filters */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search events..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">All Types</option>
                {eventTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>

              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">All Status</option>
                {eventStatuses.map(status => (
                  <option key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Events Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredEvents.length === 0 ? (
            <div className="text-center py-12">
              <FaCalendarAlt className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No events found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden"
                >
                  {/* Event Image */}
                  <div className="h-48 bg-gradient-to-br from-purple-500 to-blue-600 relative">
                    <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                    <div className="absolute top-4 left-4 flex space-x-2">
                      <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getTypeColor(event.type)}`}>
                        {event.type}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(event.status)}`}>
                        {event.status}
                      </span>
                    </div>
                  </div>

                  {/* Event Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800 mb-2 line-clamp-2">
                      {event.title}
                    </h3>
                    
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {event.description}
                    </p>

                    {/* Event Details */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <FaCalendarAlt className="mr-2 text-purple-500" />
                        <span>{formatDate(event.startDate)}</span>
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-500">
                        <FaClock className="mr-2 text-purple-500" />
                        <span>{event.startTime} - {event.endTime}</span>
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-500">
                        <FaMapMarkerAlt className="mr-2 text-purple-500" />
                        <span>{event.venue}</span>
                      </div>

                      {event.registrationRequired && (
                        <div className="flex items-center text-sm text-gray-500">
                          <FaUsers className="mr-2 text-purple-500" />
                          <span>
                            {event.currentParticipants}/{event.maxParticipants} registered
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Speaker Info */}
                    {event.speaker && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        <p className="text-sm font-semibold text-gray-800">{event.speaker.name}</p>
                        <p className="text-xs text-gray-600">
                          {event.speaker.designation}, {event.speaker.organization}
                        </p>
                      </div>
                    )}

                    {/* Prizes */}
                    {event.prizes && (
                      <div className="mb-4">
                        <p className="text-sm font-semibold text-gray-800 mb-2">Prizes:</p>
                        <div className="flex space-x-2">
                          {event.prizes.map((prize, idx) => (
                            <span key={idx} className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                              {prize}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {event.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full flex items-center"
                        >
                          <FaTag className="mr-1" />
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-purple-600 hover:bg-purple-700 text-white text-center py-2 px-4 rounded-md transition-colors duration-200 text-sm font-medium">
                        View Details
                      </button>
                      
                      {event.registrationRequired && event.status === 'upcoming' && (
                        <button className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm font-medium flex items-center">
                          <FaExternalLinkAlt className="mr-1" />
                          Register
                        </button>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default EventsPage;
