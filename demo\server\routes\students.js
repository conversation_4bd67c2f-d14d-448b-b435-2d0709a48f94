import express from 'express';
import { body, validationResult } from 'express-validator';
import Student from '../models/Student.js';
import { protect, authorize, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// @desc    Get all student achievements (public)
// @route   GET /api/students/achievements
// @access  Public
router.get('/achievements', optionalAuth, async (req, res) => {
  try {
    const { type, limit = 10, page = 1 } = req.query;
    
    // Build aggregation pipeline
    const pipeline = [
      { $match: { isActive: true } },
      { $unwind: '$achievements' },
      { $match: { 'achievements.isVerified': true } }
    ];
    
    // Filter by achievement type if specified
    if (type) {
      pipeline.push({ $match: { 'achievements.type': type } });
    }
    
    // Sort by date (newest first)
    pipeline.push({ $sort: { 'achievements.date': -1 } });
    
    // Add student info and reshape
    pipeline.push({
      $project: {
        _id: '$achievements._id',
        type: '$achievements.type',
        title: '$achievements.title',
        description: '$achievements.description',
        date: '$achievements.date',
        organization: '$achievements.organization',
        position: '$achievements.position',
        company: '$achievements.company',
        package: '$achievements.package',
        images: '$achievements.images',
        student: {
          name: '$name',
          rollNumber: '$rollNumber',
          program: '$program',
          batch: '$batch',
          profilePhoto: '$profilePhoto'
        }
      }
    });
    
    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    pipeline.push({ $skip: skip });
    pipeline.push({ $limit: parseInt(limit) });
    
    const achievements = await Student.aggregate(pipeline);
    
    // Get total count for pagination
    const totalPipeline = [
      { $match: { isActive: true } },
      { $unwind: '$achievements' },
      { $match: { 'achievements.isVerified': true } }
    ];
    
    if (type) {
      totalPipeline.push({ $match: { 'achievements.type': type } });
    }
    
    totalPipeline.push({ $count: 'total' });
    const totalResult = await Student.aggregate(totalPipeline);
    const total = totalResult.length > 0 ? totalResult[0].total : 0;
    
    res.json({
      success: true,
      data: achievements,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get achievements error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching achievements'
    });
  }
});

// @desc    Get placement statistics
// @route   GET /api/students/placement-stats
// @access  Public
router.get('/placement-stats', async (req, res) => {
  try {
    const currentYear = new Date().getFullYear();
    const { year = currentYear } = req.query;
    
    // Get placement statistics
    const stats = await Student.aggregate([
      {
        $match: {
          isActive: true,
          graduationYear: parseInt(year)
        }
      },
      {
        $group: {
          _id: null,
          totalStudents: { $sum: 1 },
          placedStudents: {
            $sum: {
              $cond: [{ $eq: ['$placement.isPlaced', true] }, 1, 0]
            }
          },
          averagePackage: {
            $avg: {
              $cond: [
                { $and: [{ $eq: ['$placement.isPlaced', true] }, { $gt: ['$placement.package', 0] }] },
                '$placement.package',
                null
              ]
            }
          },
          highestPackage: {
            $max: {
              $cond: [
                { $and: [{ $eq: ['$placement.isPlaced', true] }, { $gt: ['$placement.package', 0] }] },
                '$placement.package',
                0
              ]
            }
          }
        }
      }
    ]);
    
    // Get top recruiting companies
    const topCompanies = await Student.aggregate([
      {
        $match: {
          isActive: true,
          'placement.isPlaced': true,
          graduationYear: parseInt(year)
        }
      },
      {
        $group: {
          _id: '$placement.company',
          count: { $sum: 1 },
          averagePackage: { $avg: '$placement.package' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    
    const result = stats.length > 0 ? stats[0] : {
      totalStudents: 0,
      placedStudents: 0,
      averagePackage: 0,
      highestPackage: 0
    };
    
    const placementPercentage = result.totalStudents > 0 
      ? ((result.placedStudents / result.totalStudents) * 100).toFixed(2)
      : 0;
    
    res.json({
      success: true,
      data: {
        year: parseInt(year),
        totalStudents: result.totalStudents,
        placedStudents: result.placedStudents,
        placementPercentage: parseFloat(placementPercentage),
        averagePackage: result.averagePackage ? result.averagePackage.toFixed(2) : 0,
        highestPackage: result.highestPackage || 0,
        topCompanies: topCompanies.map(company => ({
          name: company._id,
          studentsPlaced: company.count,
          averagePackage: company.averagePackage ? company.averagePackage.toFixed(2) : 0
        }))
      }
    });
  } catch (error) {
    console.error('Get placement stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching placement statistics'
    });
  }
});

// @desc    Get student by ID
// @route   GET /api/students/:id
// @access  Private
router.get('/:id', protect, async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);
    
    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }
    
    // Check if user can access this student's data
    if (req.user.role !== 'admin' && req.user.id !== student._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    res.json({
      success: true,
      data: student
    });
  } catch (error) {
    console.error('Get student error:', error);
    
    if (error.name === 'CastError') {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while fetching student'
    });
  }
});

// @desc    Create new student
// @route   POST /api/students
// @access  Private (Admin only)
router.post('/', [
  protect,
  authorize('admin'),
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('rollNumber').trim().notEmpty().withMessage('Roll number is required'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('program').isIn(['B.Tech', 'M.Tech', 'Ph.D', 'MCA', 'BCA']).withMessage('Invalid program'),
  body('batch').notEmpty().withMessage('Batch is required'),
  body('semester').isInt({ min: 1, max: 8 }).withMessage('Semester must be between 1 and 8')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    // Check if student with roll number or email already exists
    const existingStudent = await Student.findOne({
      $or: [
        { rollNumber: req.body.rollNumber },
        { email: req.body.email }
      ]
    });
    
    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: 'Student with this roll number or email already exists'
      });
    }
    
    const student = await Student.create(req.body);
    
    res.status(201).json({
      success: true,
      message: 'Student created successfully',
      data: student
    });
  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating student'
    });
  }
});

// @desc    Update student
// @route   PUT /api/students/:id
// @access  Private (Admin or own profile)
router.put('/:id', [
  protect,
  body('name').optional().trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('semester').optional().isInt({ min: 1, max: 8 }).withMessage('Semester must be between 1 and 8')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const student = await Student.findById(req.params.id);
    
    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }
    
    // Check if user can update this student's data
    if (req.user.role !== 'admin' && req.user.id !== student._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const updatedStudent = await Student.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    res.json({
      success: true,
      message: 'Student updated successfully',
      data: updatedStudent
    });
  } catch (error) {
    console.error('Update student error:', error);
    
    if (error.name === 'CastError') {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while updating student'
    });
  }
});

export default router;
