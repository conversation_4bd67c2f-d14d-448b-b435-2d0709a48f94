import mongoose from 'mongoose';

const studentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Student name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  rollNumber: {
    type: String,
    required: [true, 'Roll number is required'],
    unique: true,
    uppercase: true,
    trim: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  program: {
    type: String,
    required: [true, 'Program is required'],
    enum: ['B.Tech', 'M.Tech', 'Ph.D', 'MCA', 'BCA']
  },
  batch: {
    type: String,
    required: [true, 'Batch year is required']
  },
  semester: {
    type: Number,
    required: [true, 'Current semester is required'],
    min: [1, 'Semester must be at least 1'],
    max: [8, 'Semester cannot exceed 8']
  },
  profilePhoto: {
    type: String,
    default: '/images/default-student-avatar.jpg'
  },
  achievements: [{
    type: {
      type: String,
      enum: ['Hackathon', 'Research Paper', 'Internship', 'Placement', 'Competition', 'Award', 'Other']
    },
    title: {
      type: String,
      required: true
    },
    description: String,
    date: Date,
    organization: String,
    position: String, // For hackathons, competitions
    company: String, // For internships, placements
    package: Number, // For placements (in LPA)
    certificate: String, // File path
    images: [String],
    isVerified: {
      type: Boolean,
      default: false
    }
  }],
  skills: [String],
  projects: [{
    title: String,
    description: String,
    technologies: [String],
    githubUrl: String,
    liveUrl: String,
    images: [String],
    startDate: Date,
    endDate: Date
  }],
  internships: [{
    company: String,
    position: String,
    duration: String,
    description: String,
    certificate: String,
    startDate: Date,
    endDate: Date
  }],
  placement: {
    isPlaced: {
      type: Boolean,
      default: false
    },
    company: String,
    position: String,
    package: Number, // in LPA
    placementDate: Date,
    offerLetter: String
  },
  cgpa: {
    type: Number,
    min: [0, 'CGPA cannot be negative'],
    max: [10, 'CGPA cannot exceed 10']
  },
  contact: {
    phone: String,
    address: String,
    linkedIn: String,
    github: String,
    portfolio: String
  },
  isActive: {
    type: Boolean,
    default: true
  },
  graduationYear: Number
}, {
  timestamps: true
});

// Index for search functionality
studentSchema.index({ 
  name: 'text', 
  rollNumber: 'text',
  skills: 'text',
  'achievements.title': 'text'
});

// Virtual for current academic year
studentSchema.virtual('currentAcademicYear').get(function() {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth();
  
  // Academic year starts in July (month 6)
  if (currentMonth >= 6) {
    return `${currentYear}-${currentYear + 1}`;
  } else {
    return `${currentYear - 1}-${currentYear}`;
  }
});

// Method to get achievements by type
studentSchema.methods.getAchievementsByType = function(type) {
  return this.achievements.filter(achievement => achievement.type === type);
};

export default mongoose.model('Student', studentSchema);
