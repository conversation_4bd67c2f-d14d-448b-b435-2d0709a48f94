import mongoose from 'mongoose';

const attendanceSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student',
    required: [true, 'Student reference is required']
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, 'Course reference is required']
  },
  faculty: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Faculty',
    required: [true, 'Faculty reference is required']
  },
  date: {
    type: Date,
    required: [true, 'Date is required'],
    default: Date.now
  },
  status: {
    type: String,
    enum: ['Present', 'Absent', 'Late', 'Excused'],
    required: [true, 'Attendance status is required'],
    default: 'Present'
  },
  timeIn: {
    type: Date,
    default: Date.now
  },
  timeOut: {
    type: Date
  },
  sessionType: {
    type: String,
    enum: ['Lecture', 'Lab', 'Tutorial', 'Seminar', 'Exam'],
    required: [true, 'Session type is required'],
    default: 'Lecture'
  },
  duration: {
    type: Number, // in minutes
    default: 60
  },
  location: {
    type: String,
    required: [true, 'Location is required']
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  },
  markedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Faculty',
    required: [true, 'Marked by faculty is required']
  },
  semester: {
    type: Number,
    required: [true, 'Semester is required'],
    min: [1, 'Semester must be at least 1'],
    max: [8, 'Semester cannot exceed 8']
  },
  academicYear: {
    type: String,
    required: [true, 'Academic year is required'],
    match: [/^\d{4}-\d{2}$/, 'Academic year must be in format YYYY-YY']
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Faculty'
  },
  verifiedAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Compound index to prevent duplicate attendance records
attendanceSchema.index({ 
  student: 1, 
  course: 1, 
  date: 1, 
  sessionType: 1 
}, { 
  unique: true,
  partialFilterExpression: { date: { $exists: true } }
});

// Index for efficient queries
attendanceSchema.index({ course: 1, date: 1 });
attendanceSchema.index({ student: 1, academicYear: 1 });
attendanceSchema.index({ faculty: 1, date: 1 });

// Virtual for attendance percentage calculation
attendanceSchema.virtual('attendancePercentage').get(function() {
  return this.status === 'Present' || this.status === 'Late' ? 100 : 0;
});

// Static method to calculate attendance percentage for a student in a course
attendanceSchema.statics.calculateAttendancePercentage = async function(studentId, courseId, academicYear) {
  const totalClasses = await this.countDocuments({
    student: studentId,
    course: courseId,
    academicYear: academicYear
  });

  const attendedClasses = await this.countDocuments({
    student: studentId,
    course: courseId,
    academicYear: academicYear,
    status: { $in: ['Present', 'Late'] }
  });

  return totalClasses > 0 ? Math.round((attendedClasses / totalClasses) * 100) : 0;
};

// Static method to get attendance summary for a course
attendanceSchema.statics.getCourseSummary = async function(courseId, date) {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);

  return await this.aggregate([
    {
      $match: {
        course: new mongoose.Types.ObjectId(courseId),
        date: { $gte: startOfDay, $lte: endOfDay }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

// Instance method to mark attendance
attendanceSchema.methods.markAttendance = function(status, markedBy, notes = '') {
  this.status = status;
  this.markedBy = markedBy;
  this.notes = notes;
  this.timeIn = new Date();
  return this.save();
};

// Pre-save middleware to set academic year if not provided
attendanceSchema.pre('save', function(next) {
  if (!this.academicYear) {
    const year = this.date.getFullYear();
    const month = this.date.getMonth();
    
    // Academic year starts in July (month 6)
    if (month >= 6) {
      this.academicYear = `${year}-${(year + 1).toString().slice(-2)}`;
    } else {
      this.academicYear = `${year - 1}-${year.toString().slice(-2)}`;
    }
  }
  next();
});

export default mongoose.model('Attendance', attendanceSchema);
