import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaTrophy, 
  FaCode, 
  FaGraduationCap, 
  FaBriefcase,
  FaAward,
  FaFilter,
  FaSearch,
  FaCalendarAlt,
  FaBuilding,
  FaMedal
} from 'react-icons/fa';

const StudentAchievementsPage = () => {
  const [achievements, setAchievements] = useState([]);
  const [filteredAchievements, setFilteredAchievements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Sample achievements data
  const sampleAchievements = [
    {
      id: 1,
      type: 'Hackathon',
      title: 'Winner - National Coding Championship 2024',
      description: 'First place in the national level coding competition organized by TechFest India with over 5000 participants.',
      student: {
        name: '<PERSON><PERSON>',
        rollNumber: 'CS2020001',
        batch: '2020',
        photo: '/images/students/rahul.jpg'
      },
      date: '2024-01-15',
      organization: 'TechFest India',
      position: '1st Place',
      prize: '₹1,00,000',
      images: ['/images/achievements/hackathon1.jpg'],
      tags: ['Programming', 'AI', 'Web Development']
    },
    {
      id: 2,
      type: 'Research Paper',
      title: 'AI in Healthcare - Published in IEEE Journal',
      description: 'Research paper on "Machine Learning Applications in Medical Diagnosis" published in IEEE Transactions on Biomedical Engineering.',
      student: {
        name: 'Priya Patel',
        rollNumber: 'CS2021002',
        batch: '2021',
        photo: '/images/students/priya.jpg'
      },
      date: '2023-12-01',
      organization: 'IEEE',
      journal: 'IEEE Transactions on Biomedical Engineering',
      images: ['/images/achievements/research1.jpg'],
      tags: ['Research', 'AI', 'Healthcare', 'Machine Learning']
    },
    {
      id: 3,
      type: 'Internship',
      title: 'Software Engineering Intern at Google',
      description: 'Completed a 3-month internship at Google India, working on cloud infrastructure and machine learning projects.',
      student: {
        name: 'Arjun Kumar',
        rollNumber: 'CS2019003',
        batch: '2019',
        photo: '/images/students/arjun.jpg'
      },
      date: '2023-08-15',
      organization: 'Google India',
      duration: '3 months',
      stipend: '₹80,000/month',
      images: ['/images/achievements/internship1.jpg'],
      tags: ['Internship', 'Google', 'Cloud', 'Software Engineering']
    },
    {
      id: 4,
      type: 'Placement',
      title: 'Software Engineer at Microsoft',
      description: 'Secured a position as Software Engineer at Microsoft with an excellent package through campus placement.',
      student: {
        name: 'Sneha Singh',
        rollNumber: 'CS2020004',
        batch: '2020',
        photo: '/images/students/sneha.jpg'
      },
      date: '2024-01-10',
      organization: 'Microsoft',
      position: 'Software Engineer',
      package: '₹28 LPA',
      images: ['/images/achievements/placement1.jpg'],
      tags: ['Placement', 'Microsoft', 'Software Engineering']
    },
    {
      id: 5,
      type: 'Competition',
      title: 'Winner - Smart India Hackathon 2023',
      description: 'Won the Smart India Hackathon 2023 in the software category with an innovative solution for rural education.',
      student: {
        name: 'Vikram Reddy',
        rollNumber: 'CS2021005',
        batch: '2021',
        photo: '/images/students/vikram.jpg'
      },
      date: '2023-09-20',
      organization: 'Government of India',
      position: '1st Place',
      prize: '₹1,00,000',
      images: ['/images/achievements/sih1.jpg'],
      tags: ['Hackathon', 'Innovation', 'Education', 'Government']
    },
    {
      id: 6,
      type: 'Award',
      title: 'Best Student of the Year 2023',
      description: 'Awarded the Best Student of the Year for outstanding academic performance and extracurricular activities.',
      student: {
        name: 'Ananya Gupta',
        rollNumber: 'CS2020006',
        batch: '2020',
        photo: '/images/students/ananya.jpg'
      },
      date: '2023-12-15',
      organization: 'CS Department',
      images: ['/images/achievements/award1.jpg'],
      tags: ['Academic Excellence', 'Leadership', 'Award']
    }
  ];

  const categories = [
    { value: 'all', label: 'All Achievements', icon: FaTrophy, count: sampleAchievements.length },
    { value: 'Hackathon', label: 'Hackathons', icon: FaCode, count: sampleAchievements.filter(a => a.type === 'Hackathon').length },
    { value: 'Research Paper', label: 'Research', icon: FaGraduationCap, count: sampleAchievements.filter(a => a.type === 'Research Paper').length },
    { value: 'Internship', label: 'Internships', icon: FaBriefcase, count: sampleAchievements.filter(a => a.type === 'Internship').length },
    { value: 'Placement', label: 'Placements', icon: FaBriefcase, count: sampleAchievements.filter(a => a.type === 'Placement').length },
    { value: 'Competition', label: 'Competitions', icon: FaMedal, count: sampleAchievements.filter(a => a.type === 'Competition').length },
    { value: 'Award', label: 'Awards', icon: FaAward, count: sampleAchievements.filter(a => a.type === 'Award').length }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setAchievements(sampleAchievements);
      setFilteredAchievements(sampleAchievements);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    let filtered = achievements;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(achievement => achievement.type === selectedCategory);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(achievement =>
        achievement.title.toLowerCase().includes(query) ||
        achievement.description.toLowerCase().includes(query) ||
        achievement.student.name.toLowerCase().includes(query) ||
        achievement.organization.toLowerCase().includes(query) ||
        achievement.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    setFilteredAchievements(filtered);
  }, [achievements, selectedCategory, searchQuery]);

  const getTypeIcon = (type) => {
    switch (type) {
      case 'Hackathon': return FaCode;
      case 'Research Paper': return FaGraduationCap;
      case 'Internship': return FaBriefcase;
      case 'Placement': return FaBriefcase;
      case 'Competition': return FaMedal;
      case 'Award': return FaAward;
      default: return FaTrophy;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'Hackathon': return 'bg-blue-100 text-blue-800';
      case 'Research Paper': return 'bg-green-100 text-green-800';
      case 'Internship': return 'bg-purple-100 text-purple-800';
      case 'Placement': return 'bg-orange-100 text-orange-800';
      case 'Competition': return 'bg-red-100 text-red-800';
      case 'Award': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading achievements...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Student Achievements
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Celebrating the outstanding accomplishments of our talented students
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filters and Search */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search achievements..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <button
                    key={category.value}
                    onClick={() => setSelectedCategory(category.value)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      selectedCategory === category.value
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <IconComponent className="text-sm" />
                    <span>{category.label}</span>
                    <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                      {category.count}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Achievements Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredAchievements.length === 0 ? (
            <div className="text-center py-12">
              <FaTrophy className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No achievements found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredAchievements.map((achievement, index) => {
                const IconComponent = getTypeIcon(achievement.type);
                return (
                  <motion.div
                    key={achievement.id}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden"
                  >
                    {/* Achievement Image */}
                    <div className="h-48 bg-gradient-to-br from-blue-500 to-purple-600 relative">
                      <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                      <div className="absolute top-4 left-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getTypeColor(achievement.type)}`}>
                          {achievement.type}
                        </span>
                      </div>
                      <div className="absolute bottom-4 left-4 text-white">
                        <IconComponent className="text-3xl mb-2" />
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-800 mb-2 line-clamp-2">
                        {achievement.title}
                      </h3>
                      
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        {achievement.description}
                      </p>

                      {/* Student Info */}
                      <div className="flex items-center mb-4">
                        <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                        <div>
                          <p className="font-semibold text-gray-800 text-sm">
                            {achievement.student.name}
                          </p>
                          <p className="text-gray-500 text-xs">
                            {achievement.student.rollNumber} • Batch {achievement.student.batch}
                          </p>
                        </div>
                      </div>

                      {/* Achievement Details */}
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center text-sm text-gray-500">
                          <FaCalendarAlt className="mr-2 text-blue-500" />
                          <span>{formatDate(achievement.date)}</span>
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-500">
                          <FaBuilding className="mr-2 text-blue-500" />
                          <span>{achievement.organization}</span>
                        </div>

                        {achievement.position && (
                          <div className="flex items-center text-sm text-gray-500">
                            <FaMedal className="mr-2 text-blue-500" />
                            <span>{achievement.position}</span>
                          </div>
                        )}

                        {achievement.prize && (
                          <div className="flex items-center text-sm font-semibold text-green-600">
                            <FaTrophy className="mr-2" />
                            <span>{achievement.prize}</span>
                          </div>
                        )}

                        {achievement.package && (
                          <div className="flex items-center text-sm font-semibold text-green-600">
                            <FaBriefcase className="mr-2" />
                            <span>{achievement.package}</span>
                          </div>
                        )}
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-2">
                        {achievement.tags.map((tag, tagIndex) => (
                          <span
                            key={tagIndex}
                            className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default StudentAchievementsPage;
