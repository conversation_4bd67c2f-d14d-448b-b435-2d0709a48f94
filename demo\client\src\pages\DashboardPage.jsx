import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaUsers, 
  FaBook, 
  FaCalendarAlt, 
  FaChartLine,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye
} from 'react-icons/fa';

const DashboardPage = () => {
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalFaculty: 0,
    totalCourses: 0,
    upcomingEvents: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  // Sample dashboard data
  useEffect(() => {
    setTimeout(() => {
      setStats({
        totalStudents: 1250,
        totalFaculty: 45,
        totalCourses: 120,
        upcomingEvents: 8
      });
      
      setRecentActivities([
        {
          id: 1,
          type: 'student',
          action: 'New student registration',
          details: '<PERSON> registered for B.Tech CS',
          timestamp: '2 hours ago'
        },
        {
          id: 2,
          type: 'event',
          action: 'Event created',
          details: 'AI Workshop 2024 scheduled',
          timestamp: '4 hours ago'
        },
        {
          id: 3,
          type: 'course',
          action: 'Course updated',
          details: 'CS301 syllabus updated',
          timestamp: '1 day ago'
        }
      ]);
      
      setLoading(false);
    }, 1000);
  }, []);

  const quickActions = [
    { icon: FaUsers, label: 'Manage Students', color: 'bg-blue-500', path: '/admin/students' },
    { icon: FaBook, label: 'Manage Courses', color: 'bg-green-500', path: '/admin/courses' },
    { icon: FaCalendarAlt, label: 'Manage Events', color: 'bg-purple-500', path: '/admin/events' },
    { icon: FaChartLine, label: 'View Analytics', color: 'bg-orange-500', path: '/admin/analytics' }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-white shadow-sm py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              Admin Dashboard
            </h1>
            <p className="text-gray-600">
              Welcome back! Here's what's happening in the CS Department.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Stats Cards */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Total Students</p>
                  <p className="text-3xl font-bold text-gray-800">{stats.totalStudents}</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <FaUsers className="text-blue-600 text-xl" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Faculty Members</p>
                  <p className="text-3xl font-bold text-gray-800">{stats.totalFaculty}</p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <FaUsers className="text-green-600 text-xl" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Total Courses</p>
                  <p className="text-3xl font-bold text-gray-800">{stats.totalCourses}</p>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <FaBook className="text-purple-600 text-xl" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Upcoming Events</p>
                  <p className="text-3xl font-bold text-gray-800">{stats.upcomingEvents}</p>
                </div>
                <div className="bg-orange-100 p-3 rounded-full">
                  <FaCalendarAlt className="text-orange-600 text-xl" />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Quick Actions and Recent Activities */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h2 className="text-xl font-bold text-gray-800 mb-6">Quick Actions</h2>
              <div className="grid grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <button
                    key={index}
                    className={`${action.color} text-white p-4 rounded-lg hover:opacity-90 transition-opacity duration-200 flex flex-col items-center space-y-2`}
                  >
                    <action.icon className="text-2xl" />
                    <span className="text-sm font-medium text-center">{action.label}</span>
                  </button>
                ))}
              </div>
            </motion.div>

            {/* Recent Activities */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h2 className="text-xl font-bold text-gray-800 mb-6">Recent Activities</h2>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-800">{activity.action}</p>
                      <p className="text-sm text-gray-600">{activity.details}</p>
                      <p className="text-xs text-gray-500 mt-1">{activity.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
              <button className="w-full mt-4 text-blue-600 hover:text-blue-800 text-sm font-medium">
                View All Activities
              </button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Management Sections */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Content Management */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h3 className="text-lg font-bold text-gray-800 mb-4">Content Management</h3>
              <div className="space-y-3">
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">Announcements</span>
                  <FaEdit className="text-gray-500" />
                </button>
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">Gallery</span>
                  <FaEdit className="text-gray-500" />
                </button>
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">News & Updates</span>
                  <FaEdit className="text-gray-500" />
                </button>
              </div>
            </motion.div>

            {/* User Management */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h3 className="text-lg font-bold text-gray-800 mb-4">User Management</h3>
              <div className="space-y-3">
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">Students</span>
                  <FaUsers className="text-gray-500" />
                </button>
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">Faculty</span>
                  <FaUsers className="text-gray-500" />
                </button>
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">Admins</span>
                  <FaUsers className="text-gray-500" />
                </button>
              </div>
            </motion.div>

            {/* System Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.9 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h3 className="text-lg font-bold text-gray-800 mb-4">System Settings</h3>
              <div className="space-y-3">
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">Site Configuration</span>
                  <FaEdit className="text-gray-500" />
                </button>
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">Email Settings</span>
                  <FaEdit className="text-gray-500" />
                </button>
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <span className="text-sm font-medium">Backup & Restore</span>
                  <FaEdit className="text-gray-500" />
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default DashboardPage;
