import express from 'express';
import { body, validationResult } from 'express-validator';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Sample labs data - in real app, this would be from database
const labs = [
  {
    id: 1,
    name: "Artificial Intelligence Lab",
    description: "State-of-the-art AI lab equipped with high-performance GPUs and specialized software for machine learning research.",
    facilities: ["NVIDIA RTX 4090 GPUs", "TensorFlow/PyTorch", "Jupyter Notebooks", "Cloud Computing Access"],
    capacity: 40,
    location: "Block A, Floor 3",
    incharge: "Dr. <PERSON>",
    equipment: [
      { name: "High-Performance Workstations", count: 20 },
      { name: "NVIDIA RTX 4090 GPUs", count: 10 },
      { name: "AI Development Software", count: "Unlimited" }
    ],
    images: ["/images/labs/ai-lab-1.jpg", "/images/labs/ai-lab-2.jpg"],
    isActive: true
  },
  {
    id: 2,
    name: "Cybersecurity Lab",
    description: "Advanced cybersecurity lab with penetration testing tools and network security equipment.",
    facilities: ["Kali Linux", "Wireshark", "Metasploit", "Network Simulators", "Firewall Systems"],
    capacity: 30,
    location: "Block B, Floor 2",
    incharge: "Prof. <PERSON> Chen",
    equipment: [
      { name: "Security Testing Workstations", count: 15 },
      { name: "Network Security Appliances", count: 5 },
      { name: "Penetration Testing Tools", count: "Multiple" }
    ],
    images: ["/images/labs/cyber-lab-1.jpg", "/images/labs/cyber-lab-2.jpg"],
    isActive: true
  },
  {
    id: 3,
    name: "IoT & Embedded Systems Lab",
    description: "Internet of Things lab with Arduino, Raspberry Pi, and various sensors for embedded system development.",
    facilities: ["Arduino Boards", "Raspberry Pi", "Sensors & Actuators", "3D Printers", "Oscilloscopes"],
    capacity: 35,
    location: "Block A, Floor 2",
    incharge: "Dr. Emily Rodriguez",
    equipment: [
      { name: "Arduino Development Kits", count: 25 },
      { name: "Raspberry Pi Boards", count: 20 },
      { name: "Various Sensors", count: "100+" },
      { name: "3D Printers", count: 3 }
    ],
    images: ["/images/labs/iot-lab-1.jpg", "/images/labs/iot-lab-2.jpg"],
    isActive: true
  },
  {
    id: 4,
    name: "Database Systems Lab",
    description: "Comprehensive database lab with enterprise-grade database management systems and analytics tools.",
    facilities: ["Oracle Database", "MySQL", "PostgreSQL", "MongoDB", "Data Analytics Tools"],
    capacity: 45,
    location: "Block B, Floor 1",
    incharge: "Prof. David Kumar",
    equipment: [
      { name: "Database Servers", count: 5 },
      { name: "Client Workstations", count: 30 },
      { name: "Enterprise Software Licenses", count: "Multiple" }
    ],
    images: ["/images/labs/db-lab-1.jpg", "/images/labs/db-lab-2.jpg"],
    isActive: true
  },
  {
    id: 5,
    name: "Software Engineering Lab",
    description: "Modern software development lab with latest IDEs, version control systems, and collaboration tools.",
    facilities: ["Visual Studio", "IntelliJ IDEA", "Git/GitHub", "Jenkins", "Docker", "Kubernetes"],
    capacity: 50,
    location: "Block A, Floor 1",
    incharge: "Dr. Lisa Wang",
    equipment: [
      { name: "Development Workstations", count: 35 },
      { name: "Collaboration Tools", count: "Cloud-based" },
      { name: "CI/CD Pipeline Tools", count: "Multiple" }
    ],
    images: ["/images/labs/se-lab-1.jpg", "/images/labs/se-lab-2.jpg"],
    isActive: true
  }
];

// @desc    Get all labs
// @route   GET /api/labs
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { search, incharge } = req.query;
    
    let filteredLabs = labs.filter(lab => lab.isActive);
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredLabs = filteredLabs.filter(lab => 
        lab.name.toLowerCase().includes(searchLower) ||
        lab.description.toLowerCase().includes(searchLower) ||
        lab.facilities.some(facility => facility.toLowerCase().includes(searchLower))
      );
    }
    
    if (incharge) {
      filteredLabs = filteredLabs.filter(lab => 
        lab.incharge.toLowerCase().includes(incharge.toLowerCase())
      );
    }
    
    res.json({
      success: true,
      data: filteredLabs,
      total: filteredLabs.length
    });
  } catch (error) {
    console.error('Get labs error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching labs'
    });
  }
});

// @desc    Get single lab
// @route   GET /api/labs/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const labId = parseInt(req.params.id);
    const lab = labs.find(lab => lab.id === labId && lab.isActive);
    
    if (!lab) {
      return res.status(404).json({
        success: false,
        message: 'Lab not found'
      });
    }
    
    res.json({
      success: true,
      data: lab
    });
  } catch (error) {
    console.error('Get lab by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching lab'
    });
  }
});

// @desc    Get lab statistics
// @route   GET /api/labs/stats
// @access  Public
router.get('/stats/overview', async (req, res) => {
  try {
    const activeLabs = labs.filter(lab => lab.isActive);
    
    const stats = {
      totalLabs: activeLabs.length,
      totalCapacity: activeLabs.reduce((sum, lab) => sum + lab.capacity, 0),
      averageCapacity: Math.round(activeLabs.reduce((sum, lab) => sum + lab.capacity, 0) / activeLabs.length),
      labsByLocation: activeLabs.reduce((acc, lab) => {
        const location = lab.location.split(',')[0]; // Get building name
        acc[location] = (acc[location] || 0) + 1;
        return acc;
      }, {}),
      totalEquipment: activeLabs.reduce((sum, lab) => {
        return sum + lab.equipment.reduce((equipSum, equip) => {
          const count = typeof equip.count === 'number' ? equip.count : 1;
          return equipSum + count;
        }, 0);
      }, 0)
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get lab stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching lab statistics'
    });
  }
});

export default router;
