import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaBook, 
  FaDownload, 
  <PERSON>a<PERSON><PERSON>, 
  <PERSON>a<PERSON>ser,
  <PERSON>a<PERSON><PERSON>er,
  FaSearch,
  FaGraduationCap,
  FaCode
} from 'react-icons/fa';

const CoursesPage = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedProgram, setSelectedProgram] = useState('all');
  const [selectedSemester, setSelectedSemester] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Sample courses data
  const sampleCourses = [
    {
      id: 1,
      code: 'CS101',
      name: 'Introduction to Programming',
      description: 'Fundamental programming concepts using Python. Covers variables, control structures, functions, and basic data structures.',
      credits: 4,
      semester: 1,
      program: 'B.Tech',
      type: 'Core',
      faculty: ['Dr. <PERSON>'],
      prerequisites: [],
      syllabus: '/files/CS101_syllabus.pdf',
      timetable: [
        { day: 'Monday', time: '9:00-10:00 AM', room: 'CS-101', type: 'Lecture' },
        { day: 'Wednesday', time: '2:00-4:00 PM', room: 'CS-Lab1', type: 'Lab' }
      ]
    },
    {
      id: 2,
      code: 'CS201',
      name: 'Data Structures and Algorithms',
      description: 'Comprehensive study of data structures and algorithms including arrays, linked lists, trees, graphs, and sorting algorithms.',
      credits: 4,
      semester: 3,
      program: 'B.Tech',
      type: 'Core',
      faculty: ['Prof. Michael Chen'],
      prerequisites: ['CS101'],
      syllabus: '/files/CS201_syllabus.pdf',
      timetable: [
        { day: 'Tuesday', time: '10:00-11:00 AM', room: 'CS-102', type: 'Lecture' },
        { day: 'Thursday', time: '3:00-5:00 PM', room: 'CS-Lab2', type: 'Lab' }
      ]
    },
    {
      id: 3,
      code: 'CS301',
      name: 'Artificial Intelligence',
      description: 'Introduction to AI concepts, search algorithms, knowledge representation, and machine learning basics.',
      credits: 4,
      semester: 5,
      program: 'B.Tech',
      type: 'Core',
      faculty: ['Dr. Sarah Johnson'],
      prerequisites: ['CS201'],
      syllabus: '/files/CS301_syllabus.pdf',
      timetable: [
        { day: 'Monday', time: '11:00 AM-12:00 PM', room: 'CS-103', type: 'Lecture' },
        { day: 'Friday', time: '2:00-4:00 PM', room: 'AI-Lab', type: 'Lab' }
      ]
    },
    {
      id: 4,
      code: 'CS401',
      name: 'Database Management Systems',
      description: 'Comprehensive study of database design, SQL, normalization, and database administration.',
      credits: 4,
      semester: 7,
      program: 'B.Tech',
      type: 'Core',
      faculty: ['Prof. David Kumar'],
      prerequisites: ['CS201'],
      syllabus: '/files/CS401_syllabus.pdf',
      timetable: [
        { day: 'Wednesday', time: '9:00-10:00 AM', room: 'CS-104', type: 'Lecture' },
        { day: 'Friday', time: '10:00 AM-12:00 PM', room: 'DB-Lab', type: 'Lab' }
      ]
    },
    {
      id: 5,
      code: 'CS501',
      name: 'Advanced Machine Learning',
      description: 'Advanced topics in machine learning including deep learning, neural networks, and modern AI techniques.',
      credits: 3,
      semester: 1,
      program: 'M.Tech',
      type: 'Core',
      faculty: ['Dr. Sarah Johnson', 'Dr. Emily Rodriguez'],
      prerequisites: ['CS301'],
      syllabus: '/files/CS501_syllabus.pdf',
      timetable: [
        { day: 'Tuesday', time: '2:00-3:00 PM', room: 'CS-201', type: 'Lecture' },
        { day: 'Thursday', time: '4:00-6:00 PM', room: 'AI-Lab', type: 'Lab' }
      ]
    }
  ];

  const programs = ['B.Tech', 'M.Tech', 'Ph.D'];
  const semesters = [1, 2, 3, 4, 5, 6, 7, 8];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setCourses(sampleCourses);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredCourses = courses.filter(course => {
    const matchesProgram = selectedProgram === 'all' || course.program === selectedProgram;
    const matchesSemester = selectedSemester === 'all' || course.semester === parseInt(selectedSemester);
    const matchesSearch = !searchQuery || 
      course.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesProgram && matchesSemester && matchesSearch;
  });

  const getTypeColor = (type) => {
    switch (type) {
      case 'Core': return 'bg-blue-100 text-blue-800';
      case 'Elective': return 'bg-green-100 text-green-800';
      case 'Lab': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading courses...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Courses & Syllabus
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Comprehensive curriculum designed to prepare you for the future of technology
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filters */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search courses..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              <select
                value={selectedProgram}
                onChange={(e) => setSelectedProgram(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Programs</option>
                {programs.map(program => (
                  <option key={program} value={program}>{program}</option>
                ))}
              </select>

              <select
                value={selectedSemester}
                onChange={(e) => setSelectedSemester(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Semesters</option>
                {semesters.map(semester => (
                  <option key={semester} value={semester}>Semester {semester}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Courses Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredCourses.length === 0 ? (
            <div className="text-center py-12">
              <FaBook className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No courses found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredCourses.map((course, index) => (
                <motion.div
                  key={course.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden"
                >
                  {/* Course Header */}
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-xl font-bold">{course.code}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getTypeColor(course.type)}`}>
                        {course.type}
                      </span>
                    </div>
                    <h4 className="text-lg font-semibold mb-2">{course.name}</h4>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="flex items-center">
                        <FaGraduationCap className="mr-1" />
                        {course.program}
                      </span>
                      <span className="flex items-center">
                        <FaBook className="mr-1" />
                        Sem {course.semester}
                      </span>
                      <span className="flex items-center">
                        <FaClock className="mr-1" />
                        {course.credits} Credits
                      </span>
                    </div>
                  </div>

                  {/* Course Content */}
                  <div className="p-6">
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {course.description}
                    </p>

                    {/* Faculty */}
                    <div className="mb-4">
                      <h5 className="font-semibold text-gray-800 mb-2 flex items-center">
                        <FaUser className="mr-2 text-blue-500" />
                        Faculty
                      </h5>
                      <div className="flex flex-wrap gap-2">
                        {course.faculty.map((faculty, idx) => (
                          <span key={idx} className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                            {faculty}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Prerequisites */}
                    {course.prerequisites.length > 0 && (
                      <div className="mb-4">
                        <h5 className="font-semibold text-gray-800 mb-2">Prerequisites:</h5>
                        <div className="flex flex-wrap gap-2">
                          {course.prerequisites.map((prereq, idx) => (
                            <span key={idx} className="bg-orange-100 text-orange-700 text-xs px-2 py-1 rounded-full">
                              {prereq}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Timetable */}
                    <div className="mb-4">
                      <h5 className="font-semibold text-gray-800 mb-2">Schedule:</h5>
                      <div className="space-y-1">
                        {course.timetable.map((schedule, idx) => (
                          <div key={idx} className="text-xs text-gray-600 flex justify-between">
                            <span>{schedule.day}</span>
                            <span>{schedule.time}</span>
                            <span className="text-blue-600">{schedule.room}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm font-medium flex items-center justify-center">
                        <FaDownload className="mr-2" />
                        Syllabus
                      </button>
                      <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md transition-colors duration-200 text-sm">
                        Details
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default CoursesPage;
