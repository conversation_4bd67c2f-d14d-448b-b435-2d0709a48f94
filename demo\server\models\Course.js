import mongoose from 'mongoose';

const courseSchema = new mongoose.Schema({
  code: {
    type: String,
    required: [true, 'Course code is required'],
    unique: true,
    uppercase: true,
    trim: true
  },
  name: {
    type: String,
    required: [true, 'Course name is required'],
    trim: true,
    maxlength: [200, 'Course name cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Course description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  credits: {
    type: Number,
    required: [true, 'Credits are required'],
    min: [1, 'Credits must be at least 1'],
    max: [10, 'Credits cannot exceed 10']
  },
  semester: {
    type: Number,
    required: [true, 'Semester is required'],
    min: [1, 'Semester must be at least 1'],
    max: [8, 'Semester cannot exceed 8']
  },
  program: {
    type: String,
    required: [true, 'Program is required'],
    enum: ['B.Tech', 'M.Tech', 'Ph.D', 'MCA', 'BCA']
  },
  type: {
    type: String,
    required: [true, 'Course type is required'],
    enum: ['Core', 'Elective', 'Lab', 'Project', 'Seminar']
  },
  prerequisites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  }],
  syllabus: {
    units: [{
      unitNumber: Number,
      title: String,
      topics: [String],
      hours: Number
    }],
    textbooks: [{
      title: String,
      author: String,
      publisher: String,
      edition: String
    }],
    references: [{
      title: String,
      author: String,
      publisher: String
    }]
  },
  syllabusFile: {
    type: String, // PDF file path
    default: null
  },
  faculty: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Faculty'
  }],
  timetable: [{
    day: {
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    },
    startTime: String,
    endTime: String,
    room: String,
    type: {
      type: String,
      enum: ['Lecture', 'Lab', 'Tutorial']
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  academicYear: {
    type: String,
    required: [true, 'Academic year is required']
  }
}, {
  timestamps: true
});

// Index for search functionality
courseSchema.index({ 
  name: 'text', 
  description: 'text', 
  code: 'text'
});

// Virtual for course duration
courseSchema.virtual('duration').get(function() {
  return `${this.credits} Credits`;
});

export default mongoose.model('Course', courseSchema);
