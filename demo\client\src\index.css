@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --primary-blue: #1e40af;
  --primary-blue-dark: #1e3a8a;
  --primary-blue-light: #3b82f6;
  --secondary-blue: #60a5fa;
  --accent-orange: #f97316;
  --text-dark: #1f2937;
  --text-light: #6b7280;
  --bg-light: #f8fafc;
  --bg-white: #ffffff;
  --border-light: #e5e7eb;
  --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--bg-white);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-blue-dark);
}

/* Custom Components */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 inline-flex items-center justify-center;
}

.btn-secondary {
  @apply bg-transparent hover:bg-blue-600 text-blue-600 hover:text-white border-2 border-blue-600 font-medium py-2 px-4 rounded-md transition-all duration-200 inline-flex items-center justify-center;
}

.btn-outline {
  @apply bg-transparent hover:bg-gray-50 text-gray-700 border border-gray-300 font-medium py-2 px-4 rounded-md transition-colors duration-200 inline-flex items-center justify-center;
}

.card {
  @apply bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6;
}

.card-hover {
  @apply transform hover:-translate-y-1 transition-transform duration-300;
}

.section-padding {
  @apply py-16 px-4;
}

.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.text-gradient {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}

.bg-gradient-primary {
  @apply bg-gradient-to-r from-blue-600 to-blue-800;
}

.bg-gradient-secondary {
  @apply bg-gradient-to-r from-purple-600 to-blue-600;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading Spinner */
.spinner {
  @apply inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
}

.spinner-small {
  @apply inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* News Ticker */
.news-ticker {
  @apply overflow-hidden whitespace-nowrap;
}

.news-ticker-content {
  @apply inline-block animate-marquee;
}

@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee {
  animation: marquee 30s linear infinite;
}
