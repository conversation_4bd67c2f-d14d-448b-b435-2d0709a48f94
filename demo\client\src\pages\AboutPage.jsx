import { motion } from 'framer-motion';
import { 
  FaGraduationCap, 
  FaEye, 
  FaBullseye, 
  FaTrophy,
  FaUsers,
  FaBook,
  FaLaptopCode,
  FaAward
} from 'react-icons/fa';

const AboutPage = () => {
  const achievements = [
    {
      icon: <FaAward className="text-4xl text-yellow-500" />,
      title: "NAAC A+ Accredited",
      description: "Recognized for excellence in education and research"
    },
    {
      icon: <FaTrophy className="text-4xl text-blue-500" />,
      title: "NBA Accredited",
      description: "All programs accredited by National Board of Accreditation"
    },
    {
      icon: <FaUsers className="text-4xl text-green-500" />,
      title: "5000+ Alumni",
      description: "Successful graduates working in top companies worldwide"
    },
    {
      icon: <FaLaptopCode className="text-4xl text-purple-500" />,
      title: "50+ Research Projects",
      description: "Ongoing innovative research in cutting-edge technologies"
    }
  ];

  const milestones = [
    { year: "1985", event: "Department established with first batch of 30 students" },
    { year: "1995", event: "Introduced M.Tech program in Computer Science" },
    { year: "2005", event: "Launched Ph.D program and research initiatives" },
    { year: "2010", event: "Achieved NBA accreditation for all programs" },
    { year: "2015", event: "Established AI and Machine Learning research center" },
    { year: "2020", event: "Received NAAC A+ grade and excellence award" },
    { year: "2024", event: "Celebrating 39 years of excellence in CS education" }
  ];

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="text-yellow-400">B.Sc. Computer Science</span>
              <br />
              <span className="text-3xl md:text-4xl">with Cognitive Systems</span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto">
              Industry Integrated Degree Program in collaboration with TATA CONSULTANCY SERVICES (TCS)
            </p>
            <p className="text-lg md:text-xl text-blue-200 max-w-3xl mx-auto mt-4">
              We bring the real-world to the classroom through TCS' Academic Interface Program (AIP)
            </p>
          </motion.div>
        </div>
      </section>

      {/* Department Overview */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                Industry-Integrated Computer Science Education
              </h2>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                Our B.Sc. Computer Science with Cognitive Systems program is an innovative
                industry-integrated degree program developed in collaboration with
                <span className="font-semibold text-blue-700"> TATA CONSULTANCY SERVICES (TCS)</span>.
                We are committed to nurturing industry-ready graduates who bridge the gap between
                academic learning and real-world applications.
              </p>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                Through TCS' Academic Interface Program (AIP), we bring the real-world to the classroom
                with industry-relevant curriculum design and deployment. Our program focuses on cognitive
                systems, building a talent pool of graduates equipped with cutting-edge skills in IT
                and emerging technologies.
              </p>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-3xl font-bold text-blue-600">39+</div>
                  <div className="text-sm text-gray-600">Years of Excellence</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-3xl font-bold text-green-600">95%</div>
                  <div className="text-sm text-gray-600">Placement Rate</div>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg p-8 text-white">
                <FaGraduationCap className="text-6xl mb-4" />
                <h3 className="text-2xl font-bold mb-4">Our Legacy</h3>
                <p className="text-blue-100 leading-relaxed">
                  From a small department with 30 students to a leading institution with 
                  over 1000 students, we have consistently maintained our commitment to 
                  excellence in education, research, and innovation.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Vision & Mission */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {/* Vision */}
            <motion.div
              variants={fadeInUp}
              className="bg-white p-8 rounded-lg shadow-lg"
            >
              <div className="flex items-center mb-6">
                <FaEye className="text-4xl text-blue-600 mr-4" />
                <h3 className="text-2xl font-bold text-gray-800">Our Vision</h3>
              </div>
              <p className="text-gray-600 leading-relaxed">
                To be a globally recognized center of excellence in computer science education 
                and research, fostering innovation and producing world-class professionals who 
                contribute to technological advancement and societal development.
              </p>
            </motion.div>

            {/* Mission */}
            <motion.div
              variants={fadeInUp}
              className="bg-white p-8 rounded-lg shadow-lg"
            >
              <div className="flex items-center mb-6">
                <FaBullseye className="text-4xl text-green-600 mr-4" />
                <h3 className="text-2xl font-bold text-gray-800">Our Mission</h3>
              </div>
              <p className="text-gray-600 leading-relaxed">
                To provide industry-integrated education in computer science with cognitive systems
                through our partnership with TCS. We aim to develop ethical, industry-ready
                professionals equipped with real-world skills and values to address global
                technological challenges and drive innovation in the IT sector.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Achievements */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Our Achievements
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Recognition and accreditations that validate our commitment to excellence
            </p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow duration-300"
              >
                <div className="flex justify-center mb-4">
                  {achievement.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">
                  {achievement.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {achievement.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Our Journey
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Key milestones in our department's growth and development
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            {milestones.map((milestone, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`flex items-center mb-8 ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
              >
                <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                  <div className="bg-white p-6 rounded-lg shadow-md">
                    <div className="text-2xl font-bold text-blue-600 mb-2">
                      {milestone.year}
                    </div>
                    <p className="text-gray-700">{milestone.event}</p>
                  </div>
                </div>
                <div className="w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg z-10"></div>
                <div className="w-1/2"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
