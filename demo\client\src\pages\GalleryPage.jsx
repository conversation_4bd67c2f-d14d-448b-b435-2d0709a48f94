import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaImage, 
  Fa<PERSON><PERSON>er, 
  FaSearch,
  FaCalendarAlt,
  FaEye,
  FaTimes
} from 'react-icons/fa';

const GalleryPage = () => {
  const [gallery, setGallery] = useState([]);
  const [filteredGallery, setFilteredGallery] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);

  // Sample gallery data
  const sampleGallery = [
    {
      id: 1,
      title: "AI Workshop 2024",
      description: "Students participating in hands-on AI and Machine Learning workshop",
      category: "Workshop",
      date: "2024-01-15",
      images: [
        { url: "/images/gallery/ai-workshop-1.jpg", caption: "Students working on ML models" },
        { url: "/images/gallery/ai-workshop-2.jpg", caption: "Expert demonstrating neural networks" },
        { url: "/images/gallery/ai-workshop-3.jpg", caption: "Group discussion on AI ethics" }
      ],
      tags: ["AI", "Machine Learning", "Workshop", "Students"]
    },
    {
      id: 2,
      title: "National Hackathon 2024",
      description: "48-hour coding marathon with innovative projects and solutions",
      category: "Competition",
      date: "2024-01-20",
      images: [
        { url: "/images/gallery/hackathon-1.jpg", caption: "Teams brainstorming innovative solutions" },
        { url: "/images/gallery/hackathon-2.jpg", caption: "Intense coding sessions" },
        { url: "/images/gallery/hackathon-3.jpg", caption: "Final presentations" },
        { url: "/images/gallery/hackathon-4.jpg", caption: "Winners receiving awards" }
      ],
      tags: ["Hackathon", "Competition", "Programming", "Innovation"]
    },
    {
      id: 3,
      title: "Lab Activities",
      description: "Students working in various computer science laboratories",
      category: "Academic",
      date: "2024-01-10",
      images: [
        { url: "/images/gallery/lab-1.jpg", caption: "Database lab session" },
        { url: "/images/gallery/lab-2.jpg", caption: "IoT project development" },
        { url: "/images/gallery/lab-3.jpg", caption: "Cybersecurity practical" }
      ],
      tags: ["Labs", "Practical", "Learning", "Technology"]
    },
    {
      id: 4,
      title: "Cultural Fest 2024",
      description: "Annual cultural festival celebrating diversity and creativity",
      category: "Cultural",
      date: "2024-01-25",
      images: [
        { url: "/images/gallery/cultural-1.jpg", caption: "Traditional dance performance" },
        { url: "/images/gallery/cultural-2.jpg", caption: "Music competition" },
        { url: "/images/gallery/cultural-3.jpg", caption: "Art exhibition" }
      ],
      tags: ["Cultural", "Festival", "Arts", "Celebration"]
    },
    {
      id: 5,
      title: "Graduation Ceremony 2023",
      description: "Celebrating the achievements of our graduating students",
      category: "Ceremony",
      date: "2023-12-15",
      images: [
        { url: "/images/gallery/graduation-1.jpg", caption: "Graduates receiving degrees" },
        { url: "/images/gallery/graduation-2.jpg", caption: "Faculty congratulating students" },
        { url: "/images/gallery/graduation-3.jpg", caption: "Group photo of graduates" }
      ],
      tags: ["Graduation", "Achievement", "Ceremony", "Success"]
    }
  ];

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'Workshop', label: 'Workshops' },
    { value: 'Competition', label: 'Competitions' },
    { value: 'Academic', label: 'Academic' },
    { value: 'Cultural', label: 'Cultural' },
    { value: 'Ceremony', label: 'Ceremonies' }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setGallery(sampleGallery);
      setFilteredGallery(sampleGallery);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    let filtered = gallery;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query) ||
        item.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    setFilteredGallery(filtered);
  }, [gallery, selectedCategory, searchQuery]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const openLightbox = (image, galleryItem) => {
    setSelectedImage({ ...image, galleryTitle: galleryItem.title });
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading gallery...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-900 to-pink-700 text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Photo Gallery
            </h1>
            <p className="text-xl md:text-2xl text-purple-100 max-w-3xl mx-auto">
              Capturing moments of learning, achievement, and celebration in our department
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filters */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search gallery..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* Category Filter */}
            <div className="flex items-center space-x-4">
              <FaFilter className="text-gray-500" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredGallery.length === 0 ? (
            <div className="text-center py-12">
              <FaImage className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No images found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredGallery.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden"
                >
                  {/* Gallery Header */}
                  <div className="p-4 border-b">
                    <h3 className="text-lg font-bold text-gray-800 mb-1">
                      {item.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {item.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                        {item.category}
                      </span>
                      <div className="flex items-center text-xs text-gray-500">
                        <FaCalendarAlt className="mr-1" />
                        {formatDate(item.date)}
                      </div>
                    </div>
                  </div>

                  {/* Image Grid */}
                  <div className="grid grid-cols-2 gap-1">
                    {item.images.slice(0, 4).map((image, imgIndex) => (
                      <div
                        key={imgIndex}
                        className={`relative cursor-pointer group ${
                          imgIndex === 0 && item.images.length > 1 ? 'col-span-2' : ''
                        }`}
                        onClick={() => openLightbox(image, item)}
                      >
                        <div className="h-32 bg-gradient-to-br from-purple-400 to-pink-500 relative overflow-hidden">
                          <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-40 transition-all duration-300"></div>
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <FaEye className="text-white text-2xl" />
                          </div>
                          {imgIndex === 3 && item.images.length > 4 && (
                            <div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center">
                              <span className="text-white text-lg font-bold">
                                +{item.images.length - 4}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Tags */}
                  <div className="p-4">
                    <div className="flex flex-wrap gap-2">
                      {item.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Lightbox */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closeLightbox}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
              <FaTimes className="text-2xl" />
            </button>
            <div className="bg-gray-200 rounded-lg max-h-[80vh] overflow-hidden">
              <div className="h-96 bg-gradient-to-br from-purple-400 to-pink-500"></div>
            </div>
            <div className="absolute bottom-4 left-4 text-white">
              <h3 className="text-lg font-bold">{selectedImage.galleryTitle}</h3>
              <p className="text-sm text-gray-300">{selectedImage.caption}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GalleryPage;
