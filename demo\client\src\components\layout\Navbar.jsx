import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaSearch, FaBars, FaTimes } from 'react-icons/fa';
import SearchModal from '../ui/SearchModal';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();

  const navLinks = [
    { name: 'Home', path: '/' },
    { name: 'About', path: '/about' },
    { name: 'Faculty', path: '/faculty' },
    { name: 'Courses', path: '/courses' },
    { name: 'Events', path: '/events' },
    { name: 'Labs', path: '/labs' },
    { name: 'Placement', path: '/placement' },
    { name: 'Attendance', path: '/attendance' },
    { name: 'Gallery', path: '/gallery' },
    { name: 'Contact', path: '/contact' },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  return (
    <>
      <motion.nav 
        className={`fixed w-full z-50 transition-all duration-300 ${
          scrolled ? 'bg-blue-900 shadow-lg py-2' : 'bg-transparent py-4'
        }`}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <Link to="/" className="flex items-center">
              <img
                src="/images/department-logo.jpg"
                alt="B.Sc. Computer Science with Cognitive Systems"
                className="h-12 w-auto mr-3 rounded-md"
              />
              <div className="text-white">
                <div className="font-bold text-lg">Computer Science</div>
                <div className="text-sm text-blue-200">with Cognitive Systems</div>
              </div>
            </Link>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-6">
              {navLinks.map((link) => (
                <Link
                  key={link.path}
                  to={link.path}
                  className={`text-white hover:text-blue-300 transition-colors ${
                    location.pathname === link.path ? 'border-b-2 border-blue-400' : ''
                  }`}
                >
                  {link.name}
                </Link>
              ))}
              <button 
                onClick={() => setSearchOpen(true)}
                className="text-white hover:text-blue-300"
                aria-label="Search"
              >
                <FaSearch />
              </button>
              <Link 
                to="/login" 
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Login
              </Link>
            </div>
            
            {/* Mobile Navigation Button */}
            <div className="flex md:hidden items-center space-x-4">
              <button 
                onClick={() => setSearchOpen(true)}
                className="text-white"
                aria-label="Search"
              >
                <FaSearch />
              </button>
              <button
                onClick={() => setIsOpen(!isOpen)}
                className="text-white"
                aria-label="Toggle menu"
              >
                {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
              </button>
            </div>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {isOpen && (
          <motion.div 
            className="md:hidden bg-blue-800"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="container mx-auto px-4 py-3">
              {navLinks.map((link) => (
                <Link
                  key={link.path}
                  to={link.path}
                  className={`block py-2 text-white hover:text-blue-300 ${
                    location.pathname === link.path ? 'font-bold' : ''
                  }`}
                >
                  {link.name}
                </Link>
              ))}
              <Link 
                to="/login" 
                className="block py-2 mt-2 bg-blue-600 hover:bg-blue-700 text-white px-4 rounded-md transition-colors text-center"
              >
                Login
              </Link>
            </div>
          </motion.div>
        )}
      </motion.nav>
      
      {/* Search Modal */}
      {searchOpen && <SearchModal onClose={() => setSearchOpen(false)} />}
      
      {/* Spacer to prevent content from hiding under fixed navbar */}
      <div className={`h-${scrolled ? '16' : '20'}`}></div>
    </>
  );
};

export default Navbar;