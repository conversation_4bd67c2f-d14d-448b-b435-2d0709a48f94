{"name": "multer", "description": "Middleware for handling `multipart/form-data`.", "version": "1.4.5-lts.2", "contributors": ["<PERSON><PERSON> <<EMAIL>> (http://www.hacksparrow.com)", "<PERSON><PERSON> <https://github.com/jpfluger>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "repository": "expressjs/multer", "keywords": ["form", "post", "multipart", "form-data", "formdata", "express", "middleware"], "dependencies": {"append-field": "^1.0.0", "busboy": "^1.0.0", "concat-stream": "^1.5.2", "mkdirp": "^0.5.4", "object-assign": "^4.1.1", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "devDependencies": {"deep-equal": "^2.0.3", "express": "^4.13.1", "form-data": "^1.0.0-rc1", "fs-temp": "^1.1.2", "mocha": "^3.5.3", "rimraf": "^2.4.1", "standard": "^14.3.3", "testdata-w3c-json-form": "^1.0.0"}, "engines": {"node": ">= 6.0.0"}, "files": ["LICENSE", "index.js", "storage/", "lib/"], "scripts": {"test": "standard && mocha"}}