import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/User.js';
import Faculty from '../models/Faculty.js';
import Course from '../models/Course.js';
import Event from '../models/Event.js';
import Student from '../models/Student.js';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cs_department');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample data
const sampleUsers = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin'
  },
  {
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    password: 'faculty123',
    role: 'faculty'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student'
  }
];

const sampleFaculty = [
  {
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    designation: 'Professor',
    qualification: ['Ph.D. in Computer Science', 'M.Tech in AI'],
    specialization: ['Artificial Intelligence', 'Machine Learning', 'Deep Learning'],
    experience: 15,
    bio: 'Dr. Sarah Johnson is a renowned expert in AI and ML with over 15 years of experience in research and teaching.',
    researchInterests: ['Neural Networks', 'Computer Vision', 'Natural Language Processing'],
    contact: {
      phone: '+91 80 1234 5680',
      office: 'Room 301, CS Block',
      officeHours: 'Mon-Fri 2:00-4:00 PM'
    },
    displayOrder: 1
  },
  {
    name: 'Prof. Michael Chen',
    email: '<EMAIL>',
    designation: 'Associate Professor',
    qualification: ['Ph.D. in Cybersecurity', 'M.S. in Computer Science'],
    specialization: ['Cybersecurity', 'Network Security', 'Cryptography'],
    experience: 12,
    bio: 'Prof. Michael Chen specializes in cybersecurity and has published numerous papers on network security.',
    researchInterests: ['Network Security', 'Blockchain', 'Digital Forensics'],
    contact: {
      phone: '+91 80 1234 5681',
      office: 'Room 302, CS Block',
      officeHours: 'Tue-Thu 3:00-5:00 PM'
    },
    displayOrder: 2
  },
  {
    name: 'Dr. Emily Rodriguez',
    email: '<EMAIL>',
    designation: 'Assistant Professor',
    qualification: ['Ph.D. in IoT Systems', 'M.Tech in Embedded Systems'],
    specialization: ['Internet of Things', 'Embedded Systems', 'Sensor Networks'],
    experience: 8,
    bio: 'Dr. Emily Rodriguez is an expert in IoT and embedded systems with focus on smart city applications.',
    researchInterests: ['Smart Cities', 'Wireless Sensor Networks', 'Edge Computing'],
    contact: {
      phone: '+91 80 1234 5682',
      office: 'Room 303, CS Block',
      officeHours: 'Mon-Wed 1:00-3:00 PM'
    },
    displayOrder: 3
  }
];

const sampleCourses = [
  {
    code: 'CS101',
    name: 'Introduction to Programming',
    description: 'Fundamental programming concepts using Python. Covers variables, control structures, functions, and basic data structures.',
    credits: 4,
    semester: 1,
    program: 'B.Tech',
    type: 'Core',
    academicYear: '2024-25',
    syllabus: {
      units: [
        {
          unitNumber: 1,
          title: 'Programming Fundamentals',
          topics: ['Variables', 'Data Types', 'Operators'],
          hours: 10
        },
        {
          unitNumber: 2,
          title: 'Control Structures',
          topics: ['Conditional Statements', 'Loops', 'Functions'],
          hours: 12
        }
      ]
    }
  },
  {
    code: 'CS301',
    name: 'Artificial Intelligence',
    description: 'Introduction to AI concepts, search algorithms, knowledge representation, and machine learning basics.',
    credits: 4,
    semester: 5,
    program: 'B.Tech',
    type: 'Core',
    academicYear: '2024-25',
    syllabus: {
      units: [
        {
          unitNumber: 1,
          title: 'AI Fundamentals',
          topics: ['History of AI', 'Intelligent Agents', 'Problem Solving'],
          hours: 12
        },
        {
          unitNumber: 2,
          title: 'Search Algorithms',
          topics: ['Uninformed Search', 'Informed Search', 'Game Playing'],
          hours: 14
        }
      ]
    }
  }
];

const sampleEvents = [
  {
    title: 'AI & Machine Learning Workshop',
    description: 'Hands-on workshop covering latest trends in AI and ML with industry experts.',
    type: 'Workshop',
    startDate: new Date('2024-02-15'),
    endDate: new Date('2024-02-15'),
    startTime: '10:00 AM',
    endTime: '4:00 PM',
    venue: 'AI Lab, Block A',
    organizer: 'CS Department',
    speaker: {
      name: 'Dr. Sarah Johnson',
      designation: 'Professor',
      organization: 'CS Department'
    },
    registrationRequired: true,
    maxParticipants: 50,
    tags: ['AI', 'Machine Learning', 'Workshop'],
    isFeatured: true,
    status: 'upcoming'
  },
  {
    title: 'National Hackathon 2024',
    description: '48-hour coding marathon with exciting prizes and opportunities to showcase programming skills.',
    type: 'Hackathon',
    startDate: new Date('2024-02-20'),
    endDate: new Date('2024-02-22'),
    startTime: '9:00 AM',
    endTime: '9:00 AM',
    venue: 'Main Auditorium',
    organizer: 'CS Department',
    registrationRequired: true,
    maxParticipants: 200,
    tags: ['Hackathon', 'Programming', 'Competition'],
    isFeatured: true,
    status: 'upcoming'
  }
];

const sampleStudents = [
  {
    name: 'Rahul Sharma',
    rollNumber: 'CS2020001',
    email: '<EMAIL>',
    program: 'B.Tech',
    batch: '2020',
    semester: 8,
    cgpa: 8.5,
    achievements: [
      {
        type: 'Hackathon',
        title: 'Winner - National Coding Championship',
        description: 'First place in national level coding competition',
        date: new Date('2023-10-15'),
        organization: 'TechFest India',
        position: '1st Place',
        isVerified: true
      },
      {
        type: 'Placement',
        title: 'Software Engineer at Google',
        description: 'Placed as Software Engineer at Google India',
        date: new Date('2024-01-10'),
        company: 'Google',
        package: 25.0,
        isVerified: true
      }
    ],
    skills: ['Python', 'Java', 'React', 'Node.js', 'Machine Learning'],
    placement: {
      isPlaced: true,
      company: 'Google',
      position: 'Software Engineer',
      package: 25.0,
      placementDate: new Date('2024-01-10')
    },
    graduationYear: 2024
  },
  {
    name: 'Priya Patel',
    rollNumber: 'CS2021002',
    email: '<EMAIL>',
    program: 'B.Tech',
    batch: '2021',
    semester: 6,
    cgpa: 9.2,
    achievements: [
      {
        type: 'Research Paper',
        title: 'Published paper on AI in Healthcare',
        description: 'Research paper published in international journal',
        date: new Date('2023-12-01'),
        organization: 'IEEE Journal',
        isVerified: true
      }
    ],
    skills: ['Python', 'TensorFlow', 'Data Science', 'Research'],
    placement: {
      isPlaced: false
    },
    graduationYear: 2025
  }
];

// Seed function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Clear existing data
    await User.deleteMany({});
    await Faculty.deleteMany({});
    await Course.deleteMany({});
    await Event.deleteMany({});
    await Student.deleteMany({});
    
    console.log('🗑️  Cleared existing data');
    
    // Create users
    const users = await User.create(sampleUsers);
    console.log(`👥 Created ${users.length} users`);
    
    // Create faculty
    const faculty = await Faculty.create(sampleFaculty);
    console.log(`👨‍🏫 Created ${faculty.length} faculty members`);
    
    // Create courses and assign faculty
    const coursesWithFaculty = sampleCourses.map((course, index) => ({
      ...course,
      faculty: [faculty[index % faculty.length]._id]
    }));
    const courses = await Course.create(coursesWithFaculty);
    console.log(`📚 Created ${courses.length} courses`);
    
    // Create events and assign creator
    const eventsWithCreator = sampleEvents.map(event => ({
      ...event,
      createdBy: users.find(user => user.role === 'admin')._id
    }));
    const events = await Event.create(eventsWithCreator);
    console.log(`📅 Created ${events.length} events`);
    
    // Create students
    const students = await Student.create(sampleStudents);
    console.log(`🎓 Created ${students.length} students`);
    
    console.log('✅ Database seeding completed successfully!');
    console.log('\n📋 Sample Login Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Faculty: <EMAIL> / faculty123');
    console.log('Student: <EMAIL> / student123');
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run seeding
const runSeed = async () => {
  await connectDB();
  await seedDatabase();
};

runSeed();
