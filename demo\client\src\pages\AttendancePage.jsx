import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaCalendarAlt,
  FaUsers,
  FaChartLine,
  FaClipboardCheck,
  FaUserCheck,
  FaUserTimes,
  FaClock,
  FaExclamationTriangle,
  FaFilter,
  FaDownload,
  FaPlus
} from 'react-icons/fa';
import AttendanceMarkingModal from '../components/attendance/AttendanceMarkingModal';

const AttendancePage = () => {
  const [attendanceData, setAttendanceData] = useState([]);
  const [courses, setCourses] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [sessionType, setSessionType] = useState('Lecture');
  const [loading, setLoading] = useState(true);
  const [showMarkingModal, setShowMarkingModal] = useState(false);
  const [stats, setStats] = useState({
    totalStudents: 0,
    presentToday: 0,
    absentToday: 0,
    averageAttendance: 0
  });

  // Sample data for demonstration
  const sampleCourses = [
    { id: 1, name: 'Data Structures and Algorithms', code: 'CS301', semester: 3 },
    { id: 2, name: 'Database Management Systems', code: 'CS302', semester: 3 },
    { id: 3, name: 'Computer Networks', code: 'CS303', semester: 3 },
    { id: 4, name: 'Operating Systems', code: 'CS304', semester: 3 },
    { id: 5, name: 'Software Engineering', code: 'CS305', semester: 3 }
  ];

  const sampleAttendance = [
    {
      id: 1,
      student: { name: 'Nivetha S', rollNumber: 'CSCS2024001', batch: '2024' },
      status: 'Present',
      timeIn: '09:15 AM',
      course: 'CS301',
      date: '2024-01-20'
    },
    {
      id: 2,
      student: { name: 'Luvomin Litta J', rollNumber: 'CSCS2024002', batch: '2024' },
      status: 'Present',
      timeIn: '09:12 AM',
      course: 'CS301',
      date: '2024-01-20'
    },
    {
      id: 3,
      student: { name: 'Sambavi D', rollNumber: 'CSCS2024003', batch: '2024' },
      status: 'Late',
      timeIn: '09:25 AM',
      course: 'CS301',
      date: '2024-01-20'
    },
    {
      id: 4,
      student: { name: 'Thirsadha Saraal J', rollNumber: 'CSCS2024004', batch: '2024' },
      status: 'Absent',
      timeIn: '-',
      course: 'CS301',
      date: '2024-01-20'
    }
  ];

  const sessionTypes = ['Lecture', 'Lab', 'Tutorial', 'Seminar', 'Exam'];

  useEffect(() => {
    // Simulate API calls
    setTimeout(() => {
      setCourses(sampleCourses);
      setAttendanceData(sampleAttendance);
      setStats({
        totalStudents: 120,
        presentToday: 95,
        absentToday: 25,
        averageAttendance: 87.5
      });
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Present': return 'text-green-600 bg-green-100';
      case 'Absent': return 'text-red-600 bg-red-100';
      case 'Late': return 'text-yellow-600 bg-yellow-100';
      case 'Excused': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Present': return FaUserCheck;
      case 'Absent': return FaUserTimes;
      case 'Late': return FaClock;
      case 'Excused': return FaExclamationTriangle;
      default: return FaUsers;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">Loading attendance data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-r from-blue-900 to-indigo-700 text-white py-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Attendance Management
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Track and manage student attendance for Computer Science with Cognitive Systems
            </p>
          </motion.div>
        </div>
      </section>

      {/* Stats Cards */}
      <section className="py-8 -mt-8 relative z-10">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Total Students</p>
                  <p className="text-3xl font-bold text-gray-800">{stats.totalStudents}</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <FaUsers className="text-blue-600 text-xl" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Present Today</p>
                  <p className="text-3xl font-bold text-green-600">{stats.presentToday}</p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <FaUserCheck className="text-green-600 text-xl" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Absent Today</p>
                  <p className="text-3xl font-bold text-red-600">{stats.absentToday}</p>
                </div>
                <div className="bg-red-100 p-3 rounded-full">
                  <FaUserTimes className="text-red-600 text-xl" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Average Attendance</p>
                  <p className="text-3xl font-bold text-blue-600">{stats.averageAttendance}%</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <FaChartLine className="text-blue-600 text-xl" />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Filters and Controls */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="bg-white rounded-lg shadow-md p-6 mb-8"
          >
            <div className="flex flex-col lg:flex-row gap-6 items-end">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Course
                </label>
                <select
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Courses</option>
                  {courses.map(course => (
                    <option key={course.id} value={course.id}>
                      {course.code} - {course.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date
                </label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Session Type
                </label>
                <select
                  value={sessionType}
                  onChange={(e) => setSessionType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {sessionTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div className="flex gap-3">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors duration-200 flex items-center">
                  <FaFilter className="mr-2" />
                  Filter
                </button>
                <button
                  onClick={() => setShowMarkingModal(true)}
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition-colors duration-200 flex items-center"
                >
                  <FaPlus className="mr-2" />
                  Mark Attendance
                </button>
              </div>
            </div>
          </motion.div>

          {/* Attendance Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-white rounded-lg shadow-md overflow-hidden"
          >
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">
                Attendance Records - {selectedDate}
              </h3>
              <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center text-sm">
                <FaDownload className="mr-2" />
                Export
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Student
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Roll Number
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Time In
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {attendanceData.map((record) => {
                    const StatusIcon = getStatusIcon(record.status);
                    return (
                      <tr key={record.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {record.student.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                Batch {record.student.batch}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.student.rollNumber}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                            <StatusIcon className="mr-1" />
                            {record.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.timeIn}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button className="text-blue-600 hover:text-blue-900 mr-3">
                            Edit
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            Delete
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Attendance Marking Modal */}
      <AttendanceMarkingModal
        isOpen={showMarkingModal}
        onClose={() => setShowMarkingModal(false)}
        course={courses.find(c => c.id.toString() === selectedCourse)}
        date={selectedDate}
        sessionType={sessionType}
      />
    </div>
  );
};

export default AttendancePage;
